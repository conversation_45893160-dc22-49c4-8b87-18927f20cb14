/* montserrat-regular - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Regular'), local('Montserrat-Regular'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 400;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Italic'), local('Montserrat-Italic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-500 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Medium'), local('Montserrat-Medium'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-500italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 500;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Medium Italic'), local('Montserrat-MediumItalic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-600 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.eot'); /* IE9 Compat Modes */
  src: local('Montserrat SemiBold'), local('Montserrat-SemiBold'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-600italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 600;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat SemiBold Italic'), local('Montserrat-SemiBoldItalic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* roboto-regular - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Roboto'), local('Roboto'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.svg#Roboto') format('svg'); /* Legacy iOS */
}
/* roboto-italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto italic';
  font-style: italic;
  font-weight: 500;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot'); /* IE9 Compat Modes */
  src: local('Roboto Italic'), local('Roboto-Italic'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.svg#Roboto') format('svg'); /* Legacy iOS */
}
/* roboto-700 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto latin';
  font-style: normal;
  font-weight: 700;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.eot'); /* IE9 Compat Modes */
  src: local('Roboto Bold'), local('Roboto-Bold'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.svg#Roboto') format('svg'); /* Legacy iOS */
}


/* open-sans-300 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.svg#OpenSans') format('svg'); /* Legacy iOS */
}
/* open-sans-300italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 300;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.svg#OpenSans') format('svg'); /* Legacy iOS */
}
/* open-sans-regular - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.svg#OpenSans') format('svg'); /* Legacy iOS */
}
/* open-sans-italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 400;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.svg#OpenSans') format('svg'); /* Legacy iOS */
}
/* open-sans-700 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.svg#OpenSans') format('svg'); /* Legacy iOS */
}
/* open-sans-700italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 700;
  src: url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.woff') format('woff'), /* Modern Browsers */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Opensans/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.svg#OpenSans') format('svg'); /* Legacy iOS */
}

/* lato-300 - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 300;
  src: url('Lato/lato-v17-latin-ext_latin-300.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-300.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-300.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-300.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-300.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-300.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-300italic - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 300;
  src: url('Lato/lato-v17-latin-ext_latin-300italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-300italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-300italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-300italic.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-300italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-300italic.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-regular - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  src: url('Lato/lato-v17-latin-ext_latin-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-regular.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-regular.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-italic - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 400;
  src: url('Lato/lato-v17-latin-ext_latin-italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-italic.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-italic.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-700 - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: url('Lato/lato-v17-latin-ext_latin-700.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-700.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-700.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-700.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-700italic - latin-ext_latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 700;
  src: url('Lato/lato-v17-latin-ext_latin-700italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Lato/lato-v17-latin-ext_latin-700italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Lato/lato-v17-latin-ext_latin-700italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-700italic.woff') format('woff'), /* Modern Browsers */
       url('Lato/lato-v17-latin-ext_latin-700italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Lato/lato-v17-latin-ext_latin-700italic.svg#Lato') format('svg'); /* Legacy iOS */
}

/* fira-sans-300 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: normal;
  font-weight: 300;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-regular - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: normal;
  font-weight: 400;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-300italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: italic;
  font-weight: 300;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-500 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: normal;
  font-weight: 500;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: italic;
  font-weight: 400;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-500italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: italic;
  font-weight: 500;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-500italic.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-700 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: normal;
  font-weight: 700;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700.svg#FiraSans') format('svg'); /* Legacy iOS */
}
/* fira-sans-700italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
  font-family: 'Fira Sans';
  font-style: italic;
  font-weight: 700;
  src: url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.woff2') format('woff2'), /* Super Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.woff') format('woff'), /* Modern Browsers */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Firasans/fira-sans-v10-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-700italic.svg#FiraSans') format('svg'); /* Legacy iOS */
}

/* mansalva-regular - latin */
@font-face {
  font-family: 'Mansalva';
  font-style: normal;
  font-weight: 400;
  src: url('Mansalva/mansalva-v2-latin-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('Mansalva/mansalva-v2-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('Mansalva/mansalva-v2-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('Mansalva/mansalva-v2-latin-regular.woff') format('woff'), /* Modern Browsers */
       url('Mansalva/mansalva-v2-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('Mansalva/mansalva-v2-latin-regular.svg#Mansalva') format('svg'); /* Legacy iOS */
}

/* droid-sans-regular - latin */
@font-face {
  font-family: 'Droid Sans';
  font-style: normal;
  font-weight: 400;
  src: local(''),
       url('Droidsans/droid-sans-v12-latin-regular.woff2') format('woff2'); /* Super Modern Browsers */
}

/* droid-sans-700 - latin */
@font-face {
  font-family: 'Droid Sans';
  font-style: normal;
  font-weight: 700;
  src:  local(''),
        url('Droidsans/droid-sans-v12-latin-700.woff2') format('woff2'); /* Super Modern Browsers */
}

/* HCLTechRoobert-regular */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: normal;
  font-weight: 400;
  src: local('HCLTechRoobert Regular'), local('HCLTechRoobert-Regular'),
    url('HCLTechRoobert/HCLTechRoobert-Regular.ttf') format('truetype');
}

/* HCLTechRoobert-regular-italic */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: italic;
  font-weight: 400;
  src: local('HCLTechRoobert Regular Italic'), local('HCLTechRoobert-Regular-Italic'),
    url('HCLTechRoobert/HCLTechRoobert-RegularItalic.ttf') format('truetype');
}

/* HCLTechRoobert-medium-italic */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: italic;
  font-weight: 500;
  src: local('HCLTechRoobert Medium Italic'), local('HCLTechRoobert-Medium-Italic'),
    url('HCLTechRoobert/HCLTechRoobert-MediumItalic.ttf') format('truetype');
}

/* HCLTechRoobert-medium */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: normal;
  font-weight: 500;
  src: local('HCLTechRoobert Medium'), local('HCLTechRoobert-Medium'),
    url('HCLTechRoobert/HCLTechRoobert-Medium.ttf') format('truetype');
}

/* HCLTechRoobert-bold-italic */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: italic;
  font-weight: bold;
  src: local('HCLTechRoobert Bold Italic'), local('HCLTechRoobert-Bold-Italic'),
    url('HCLTechRoobert/HCLTechRoobert-BoldItalic.ttf') format('truetype');
}

/* HCLTechRoobert-bold */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: normal;
  font-weight: bold;
  src: local('HCLTechRoobert Bold'), local('HCLTechRoobert-Bold'),
    url('HCLTechRoobert/HCLTechRoobert-Bold.ttf') format('truetype');
}

/* HCLTechRoobert-heavy-italic */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: italic;
  font-weight: bolder;
  src: local('HCLTechRoobert Bolder Italic'), local('HCLTechRoobert-Bolder-Italic'),
    url('HCLTechRoobert/HCLTechRoobert-ExtraBoldItalic.ttf') format('truetype');
}

/* HCLTechRoobert-heavy */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: normal;
  font-weight: bolder;
  src: local('HCLTechRoobert Bolder'), local('HCLTechRoobert-Bolder'),
    url('HCLTechRoobert/HCLTechRoobert-ExtraBold.ttf') format('truetype');
}

/* HCLTechRoobert-light */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: normal;
  font-weight: lighter;
  src: local('HCLTechRoobert Light'), local('HCLTechRoobert-Light'),
    url('HCLTechRoobert/HCLTechRoobert-Light.ttf') format('truetype');
}

/* HCLTechRoobert-light-italic */
@font-face {
  font-family: 'HCLTechRoobert';
  font-style: italic;
  font-weight: lighter;
  src: local('HCLTechRoobert Light Italic'), local('HCLTechRoobert-Light-Italic'),
    url('HCLTechRoobert/HCLTechRoobert-LightItalic.ttf') format('truetype');
}