.button-dynamic-setting-form {
    .rule-info-container {
        letter-spacing: normal;
        text-align: left;
        color: #6D7692;
        float: left;
        .rule-name {
            font-size: 14px;
            line-height: 18px;
            letter-spacing: normal;
            font-family: Montserrat;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            padding: 20px 5px 20px 15px;
            font-weight: 600;
        }
        .hyperlink-url-container {
            overflow: hidden;
            .url-btn-info {
                font-size: 12px;
                line-height: 15px;
                letter-spacing: 0.4px;
                font-weight: 500;
            }
            .url-link {
                color: #0078D8;
            }
            .hcl-icon-sorting {
                font-size: 13px;
                &:before {
                    transform: rotate(225deg);
                }
            }
            .mt-minus-2 {
                margin-top: -2px;
            }
        }
    }
    .actions-container {
        padding: 15px 0px;
        color: #6d7692;
        float: left;
        width: 10%;
        display: inline-flex;
        .edit-delete-icon {
            height: 30px;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;
    
        }
        .mr-10 {
            margin-right: 10px;
        }
    }
    .add-dynamic-button-container {
        text-align: center;
    }
}