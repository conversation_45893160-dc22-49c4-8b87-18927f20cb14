import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { VerticalAlign } from '../config/email-common-elements';

/**
 * This directive dynamically applies vertical alignment styles to an element.
 */
@UntilDestroy()
@Directive({
  selector: '[elementVerticalAlign]',
  standalone: true,
})
export class VerticalAlignmentDirective {
  /**
   * The vertical alignment subject to track changes
   */
  private verticalAlignSubject = new BehaviorSubject<string | undefined>(undefined);
  protected verticalAlign$ = this.verticalAlignSubject.asObservable();

  @Input() set elementVerticalAlign(align: string | undefined | null) {
    this.verticalAlignSubject.next(align === null ? undefined : align);
  }

  /**
   * Constructor to inject dependencies
   */
  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.verticalAlign$.pipe(untilDestroyed(this)).subscribe((align) => {
      if (align) {
        this.update(align);
      }
    });
  }

  /**
   * Updates the vertical alignment style
   */
  private update(align: string): void {
    const styles = this.createVerticalAlignStyles(align);
    if (styles) {
      Object.keys(styles).forEach((key) => {
        this.renderer.setStyle(this.el.nativeElement, key, styles[key]);
      });
    }
  }

  /**
   * Creates the vertical alignment styles based on the alignment type
   */
  private createVerticalAlignStyles(align: string): { [key: string]: string } {
    const vAlign = align as VerticalAlign;
    switch (vAlign) {
      case 'top':
        return { alignContent: 'flex-start' };
      case 'middle':
        return { alignContent: 'center' };
      case 'bottom':
        return { alignContent: 'flex-end' };
      default:
        return {};
    }
  }
}