/**
 * Unit tests for style utility functions
 */

import { TextEditorStyles } from '../editor.model';
import {
  computeEditorStyles,
  getColorStyles,
  getFontStyles,
  getLineHeightStyles,
  getPaddingStyles,
  hasPaddingValues
} from './style.utils';

describe('Style Utils', () => {
  describe('computeEditorStyles', () => {
    it('should return empty object when no styles provided', () => {
      expect(computeEditorStyles()).toEqual({});
      expect(computeEditorStyles(undefined)).toEqual({});
    });

    it('should compute complete styles object', () => {
      const styles: TextEditorStyles = {
        color: '#333',
        font: {
          family: 'Arial',
          size: 16,
          weight: 'bold'
        },
        lineHeight: {
          value: 1.5,
          unit: 'em'
        },
        padding: {
          top: 10,
          right: 15,
          bottom: 10,
          left: 15
        }
      };

      const result = computeEditorStyles(styles);

      expect(result).toEqual({
        color: '#333',
        'font-family': 'Arial',
        'font-size': '16px',
        'font-weight': 'bold',
        'line-height': '1.5em',
        padding: '10px 15px 10px 15px'
      });
    });
  });

  describe('getColorStyles', () => {
    it('should return color style when provided', () => {
      expect(getColorStyles('#333')).toEqual({ color: '#333' });
    });

    it('should return empty object when no color provided', () => {
      expect(getColorStyles()).toEqual({});
      expect(getColorStyles(undefined)).toEqual({});
    });
  });

  describe('getFontStyles', () => {
    it('should return font styles when provided', () => {
      const font = {
        family: 'Arial',
        size: 16,
        weight: 'bold',
        style: 'italic'
      };

      expect(getFontStyles(font)).toEqual({
        'font-family': 'Arial',
        'font-size': '16px',
        'font-weight': 'bold',
        'font-style': 'italic'
      });
    });

    it('should return empty object when no font provided', () => {
      expect(getFontStyles()).toEqual({});
      expect(getFontStyles(undefined)).toEqual({});
    });
  });

  describe('getLineHeightStyles', () => {
    it('should return line-height style when provided', () => {
      const lineHeight = { value: 1.5, unit: 'em' };
      expect(getLineHeightStyles(lineHeight)).toEqual({ 'line-height': '1.5em' });
    });

    it('should return empty object when incomplete data', () => {
      expect(getLineHeightStyles()).toEqual({});
      expect(getLineHeightStyles({ value: 1.5 })).toEqual({});
      expect(getLineHeightStyles({ unit: 'em' })).toEqual({});
    });
  });

  describe('getPaddingStyles', () => {
    it('should return padding style when provided', () => {
      const padding = { top: 10, right: 15, bottom: 10, left: 15 };
      expect(getPaddingStyles(padding)).toEqual({ padding: '10px 15px 10px 15px' });
    });

    it('should return empty object when no padding values', () => {
      expect(getPaddingStyles()).toEqual({});
      expect(getPaddingStyles({})).toEqual({});
    });
  });

  describe('hasPaddingValues', () => {
    it('should return true when padding values exist', () => {
      expect(hasPaddingValues({ top: 10 })).toBe(true);
      expect(hasPaddingValues({ right: 5 })).toBe(true);
      expect(hasPaddingValues({ bottom: 0 })).toBe(false); // 0 is falsy
      expect(hasPaddingValues({ left: 15 })).toBe(true);
    });

    it('should return false when no padding values', () => {
      expect(hasPaddingValues({})).toBe(false);
    });
  });

});
