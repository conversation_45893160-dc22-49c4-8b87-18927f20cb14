<ng-container
  *ngrxLet="dropListRegistry.currentActiveDropList$; let activeDropList"
>
  <ng-container *ngrxLet="active$; let active">
    <ng-container *ngrxLet="hoverActive$; let hovered">
      <!--The drag handle-->
      <div
        class="element-drag-handle"
        [ngClass]="{
          'element-drag-handle': !invertedDraggable,
          'inverted-drag-handle': invertedDraggable,
        }"
        (mouseenter)="mouseEnter()"
        (mouseleave)="mouseLeave()"
        *ngIf="
          (active && draggable && !activeDropList) || (hovered && draggable)
        "
        cdkDragHandle
      >
        <!-- TODO : material drag_indicator is not working -->
        <unica-icon
          class="icon"
          [name]="'unica_drag_indicator'"
          [size]="'small'"
        ></unica-icon>
      </div>
    </ng-container>
    <ng-container>
      <div
        class="hide-on-icon-tool"
        *ngIf="active && ((hideOn$ | async) ?? null)"
      >
        <unica-icon
          [name]="'unica_hide_on_' + (hideOn$ | async) ?? null"
          [className]="'canvas-hide-on-icon'"
        ></unica-icon>
      </div>
    </ng-container>
    <div
      class="focus-overlay"
      cdkOverlayOrigin
      #container
      *ngrxLet="dragInProgress$; let dragInProgress"
      #elementTarget="cdkOverlayOrigin"
      (mouseenter)="mouseEnter()"
      (mouseleave)="mouseLeave()"
      [elementWidth]="(width$ | async) ?? null"
      [elementBackground]="(background$ | async) ?? null"
      [elementPadding]="(padding$ | async) ?? null"
      [elementBorder]="(border$ | async) ?? null"
      [elementAlign]="(align$ | async) ?? null"
      [ngClass]="(active ?? false) ? activeCss : ''"
    >
      <ng-content></ng-content>

      <!--This is the template element toolbar -->
      <ng-template
        cdkConnectedOverlay
        [cdkConnectedOverlayOrigin]="elementTarget"
        [cdkConnectedOverlayPositions]="toolbarPosition"
        [cdkConnectedOverlayOpen]="active && !activeDropList && !dragInProgress"
      >
        <div class="element-toolbar">
          <div
            class="toolbar-item"
            *ngFor="let i of items"
            (click)="toolbarItemSelected(i, $event)"
            (keydown)="toolbarItemSelected(i, $event)"
            tabindex="0"
            [attr.title]="i.label"
          >
            <unica-icon
              [name]="i.icon"
              [className]="'toolbar-icon'"
              [size]="'small'"
            ></unica-icon>
          </div>
        </div>
      </ng-template>

      <ng-template
        cdkConnectedOverlay
        [cdkConnectedOverlayPush]="true"
        [cdkConnectedOverlayOrigin]="elementTarget"
        [cdkConnectedOverlayPositions]="structurePosition"
        [cdkConnectedOverlayOpen]="
          active &&
          structureToolbarItems.length > 0 &&
          !activeDropList &&
          !dragInProgress
        "
      >
        <div class="structure-toolbar" style="display: flex">
          <div
            class="toolbar-item"
            *ngFor="let i of structureToolbarItems"
            (click)="toolbarItemSelected(i, $event)"
            (keydown)="toolbarItemSelected(i, $event)"
            tabindex="0"
            [attr.title]="i.label"
          >
            <unica-icon
              [name]="i.icon"
              [className]="'toolbar-icon'"
              [size]="'small'"
            ></unica-icon>
          </div>
        </div>
      </ng-template>
    </div>
  </ng-container>
</ng-container>
