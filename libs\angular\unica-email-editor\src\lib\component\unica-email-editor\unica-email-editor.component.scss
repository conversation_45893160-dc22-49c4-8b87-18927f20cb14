.unica-email-editor {
  width: 100%;
  height: 100%;

  .drawer{
    width: 70px;
    position: relative;

    .drawer-drag-handle {
      position: absolute;
      top: 50%;
      right: -8px;
      transform: translateY(-50%);
      width: 16px;
      height: 40px;
      cursor: grab;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-50%) scale(1.05);
      }

      &:active,
      &.cdk-drag-dragging {
        cursor: grabbing;
        background: rgba(3, 141, 153, 0.1);
        border-color: var(--unica-primary, #038D99);
      }

      .drag-handle-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .drag-dots {
          display: flex;
          flex-direction: column;
          gap: 2px;

          span {
            width: 3px;
            height: 3px;
            background: #666;
            border-radius: 50%;
            display: block;
            transition: background-color 0.2s ease;
          }
        }
      }

      &:hover .drag-dots span {
        background: var(--unica-primary, #038D99);
      }
    }

    // Adjust drag handle position for left-positioned drawer
    &.mat-drawer-start .drawer-drag-handle {
      right: auto;
      left: -8px;
    }
  }

  mat-drawer-container {
    height: 100%;
    width: 100%;
  }
}
