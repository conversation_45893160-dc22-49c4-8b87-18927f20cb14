.unica-email-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  mat-drawer-container {
    height: 100%;
    width: 100%;
    flex: 1;
    display: flex;
    position: relative;

    // Drop zone styles inside the container
    .drop-zone-left,
    .drop-zone-right {
      position: absolute;
      top: 0;
      width: 80px;
      height: 100%;
      background: rgba(255, 0, 0, 0.3); // Red background for testing
      border: 2px solid red; // Solid red border for testing
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: all 0.3s ease;
      pointer-events: auto;
      opacity: 1; // Always visible for testing

      &.visible {
        opacity: 1;
        background: rgba(3, 141, 153, 0.3);
        border: 2px dashed rgba(3, 141, 153, 0.5);
      }

      &.active {
        opacity: 1;
        background: rgba(3, 141, 153, 0.5);
        border-color: var(--unica-primary, #038D99);
        border-style: solid;
        pointer-events: auto;
      }

      .drop-zone-indicator {
        text-align: center;
        color: var(--unica-primary, #038D99);
        font-weight: 600;

        .drop-zone-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }

        .drop-zone-text {
          font-size: 12px;
          writing-mode: vertical-rl;
          text-orientation: mixed;
        }
      }
    }

    .drop-zone-left {
      left: 0;
    }

    .drop-zone-right {
      right: 0;
    }
  }

  .drawer{
    width: 70px !important;
    position: relative;
    background-color: var(--unica-bg-page, #ECFEFF);
    border-right: 1px solid #ddd;
    min-height: 100vh;
    z-index: 100;

    // Ensure the drawer is always visible
    &.mat-drawer {
      position: relative !important;
      transform: none !important;
    }

    .drawer-drag-handle {
      position: absolute;
      top: 50%;
      right: -8px;
      transform: translateY(-50%);
      width: 16px;
      height: 40px;
      cursor: grab;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-50%) scale(1.05);
      }

      &:active,
      &.cdk-drag-dragging {
        cursor: grabbing;
        background: rgba(3, 141, 153, 0.1);
        border-color: var(--unica-primary, #038D99);
      }

      .drag-handle-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .drag-dots {
          display: flex;
          flex-direction: column;
          gap: 2px;

          span {
            width: 3px;
            height: 3px;
            background: #666;
            border-radius: 50%;
            display: block;
            transition: background-color 0.2s ease;
          }
        }
      }

      &:hover .drag-dots span {
        background: var(--unica-primary, #038D99);
      }
    }

    // Adjust drag handle position for left-positioned drawer
    &.mat-drawer-start .drawer-drag-handle {
      right: auto;
      left: -8px;
    }
  }

  // Ensure drawer content is visible
  mat-drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  // Make sure the drawer is visible when positioned on the left
  .mat-drawer-start {
    border-right: 1px solid #ddd;
    border-left: none;
  }

  // Make sure the drawer is visible when positioned on the right
  .mat-drawer-end {
    border-left: 1px solid #ddd;
    border-right: none;
  }
}
