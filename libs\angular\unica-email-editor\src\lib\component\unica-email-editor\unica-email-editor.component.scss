.unica-email-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; // Prevent scrollbars from fixed positioned drop zones

  // Drop zone styles positioned over the entire component
  .drop-zone-left,
  .drop-zone-right {
    position: absolute; // Fixed to viewport - no space in document flow
    top: 0;
    width: 60px; // Smaller width to be less intrusive
    height: 100vh; // Full viewport height
    background: rgba(3, 141, 153, 0.1);
    border: 2px dashed rgba(3, 141, 153, 0.3);
    display: none; // Hidden by default - takes NO space
    align-items: center;
    justify-content: center;
    z-index: 9999; // High z-index to overlay everything
    transition: all 0.2s ease;
    pointer-events: none; // Don't interfere with normal interactions
    box-sizing: border-box; // Include border in width calculation
    margin: 0; // No margins
    padding: 0; // No padding
    overflow: hidden; // Prevent any overflow

    &.visible {
      display: flex;
      pointer-events: auto;
      opacity: 1;
      background: rgba(3, 141, 153, 0.2);
      border-color: var(--unica-primary, #038D99);

      &:hover,
      &.hover {
        background: rgba(3, 141, 153, 0.4);
        border-style: solid;
        border-width: 3px;
      }
    }

    .drop-zone-indicator {
      text-align: center;
      color: var(--unica-primary, #038D99);
      font-weight: 600;

      .drop-zone-icon {
        font-size: 24px;
        margin-bottom: 8px;
        color: white;
      }

      .drop-zone-text {
        font-size: 12px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
        color: white;
        font-weight: bold;
      }
    }
  }

  .drop-zone-left {
    left: 0;
    border-right: 2px dashed rgba(3, 141, 153, 0.3);
    border-left: none;
  }

  .drop-zone-right {
    right: 0;
    border-left: 2px dashed rgba(3, 141, 153, 0.3);
    border-right: none;
  }

  mat-drawer-container {
    height: 100%;
    width: 100%;
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden; // Prevent any scrollbars from drop zones
  }

  .drawer{
    width: 70px !important;
    position: relative;
    min-height: 100vh;
    z-index: 100;

    // Ensure the drawer is always visible
    &.mat-drawer {
      position: relative !important;
      transform: none !important;
    }

    .drawer-drag-handle {
      position: absolute;
      top: 50%;
      right: -8px;
      transform: translateY(-50%);
      width: 16px;
      height: 40px;
      cursor: grab;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-50%) scale(1.05);
      }

      &:active,
      &.cdk-drag-dragging {
        cursor: grabbing;
        background: rgba(3, 141, 153, 0.1);
        border-color: var(--unica-primary, #038D99);
      }

      .drag-handle-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .drag-dots {
          display: flex;
          flex-direction: column;
          gap: 2px;

          span {
            width: 3px;
            height: 3px;
            background: #666;
            border-radius: 50%;
            display: block;
            transition: background-color 0.2s ease;
          }
        }
      }

      &:hover .drag-dots span {
        background: var(--unica-primary, #038D99);
      }
    }

    // Adjust drag handle position for left-positioned drawer
    &.mat-drawer-start .drawer-drag-handle {
      right: auto;
      left: -8px;
    }
  }

  // Ensure drawer content is visible
  mat-drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
