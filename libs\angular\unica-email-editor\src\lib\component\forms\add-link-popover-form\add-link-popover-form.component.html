<form [formGroup]="linkPopoverForm" (ngSubmit)="onSave()" novalidate>
    <div class="link-popover-container mat-elevation-z4"
        [class.expanded]="linkPopoverForm.get('linkUrl')?.invalid && linkPopoverForm.get('linkUrl')?.touched && linkPopoverForm.get('linkUrl')?.errors?.['pattern']">
        <div class="link-popover-header">
            <span class="link-popover-title">Add Link to the {{ blockName }}</span>
            <button mat-icon-button aria-label="Close" (click)="onCancel()" class="close-btn">
                <mat-icon>close</mat-icon>
            </button>
        </div>

        <div class="link-popover-content">
            <!-- URL Option -->
            <mat-radio-button name="linkType" [checked]="linkPopoverForm.get('linkType')?.value === 'url'"
                (change)="setLinkType('url')">
                URL
            </mat-radio-button>
            <mat-form-field appearance="outline" class="unica-input full-width link-popover-input">
                <input matInput formControlName="url" placeholder="Enter URL"
                    [disabled]="linkPopoverForm.get('linkType')?.value !== 'url'" />
                @if (linkPopoverForm.get('url')?.touched && linkPopoverForm.get('url')?.errors) {
                <mat-error>
                    {{ getErrorMessage(linkPopoverForm.get('url')?.errors) }}
                </mat-error>
                }
            </mat-form-field>
            <!-- LP Option -->
            <mat-radio-button name="linkType" [checked]="linkPopoverForm.get('linkType')?.value === 'lp'"
                (change)="setLinkType('lp')">
                LP
            </mat-radio-button>
            <mat-form-field appearance="outline" class="unica-input full-width link-popover-input">
                <input matInput formControlName="landingPage" placeholder="Select a page" />
            </mat-form-field>

            <button mat-stroked-button color="accent" class="browse-btn save-btn" (click)="onBrowseLP($event)"
                [disabled]="linkPopoverForm.get('linkType')?.value !== 'lp'">
                Browse
            </button>

            <button mat-raised-button color="primary" type="submit" class="save-btn"
                [disabled]="linkPopoverForm.invalid">
                Save
            </button>
        </div>
    </div>
</form>