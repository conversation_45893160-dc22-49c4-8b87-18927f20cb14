import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UnicaEmailEditorComponent } from './unica-email-editor.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('UnicaEmailEditorComponent', () => {
  let component: UnicaEmailEditorComponent;
  let fixture: ComponentFixture<UnicaEmailEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UnicaEmailEditorComponent, NoopAnimationsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(UnicaEmailEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default elementToolbarPosition as right', () => {
    expect(component.elementToolbarPosition).toBe('right');
  });

  it('should render drop zones', () => {
    const compiled = fixture.nativeElement;
    const leftDropZone = compiled.querySelector('.drop-zone-left');
    const rightDropZone = compiled.querySelector('.drop-zone-right');

    expect(leftDropZone).toBeTruthy();
    expect(rightDropZone).toBeTruthy();
  });

  it('should pass isDraggable to email-editor-element-toolbar', () => {
    const compiled = fixture.nativeElement;
    const toolbar = compiled.querySelector('email-editor-element-toolbar');
    expect(toolbar).toBeTruthy();
  });

  describe('onToolbarPositionChange', () => {
    it('should update elementToolbarPosition when called', () => {
      component['onToolbarPositionChange']('left');
      expect(component.elementToolbarPosition).toBe('left');

      component['onToolbarPositionChange']('right');
      expect(component.elementToolbarPosition).toBe('right');
    });
  });

  describe('onDropZoneDrop', () => {
    it('should update position and reset dragging state', () => {
      const mockEvent = new DragEvent('drop');
      spyOn(mockEvent, 'preventDefault');

      component['isDraggingToolbar'] = true;
      component['onDropZoneDrop'](mockEvent, 'left');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.elementToolbarPosition).toBe('left');
      expect(component['isDraggingToolbar']).toBe(false);
    });
  });

  describe('onDropZoneDragOver', () => {
    it('should prevent default and set drop effect', () => {
      const mockEvent = new DragEvent('dragover');
      Object.defineProperty(mockEvent, 'dataTransfer', {
        value: { dropEffect: '' },
        writable: true
      });
      spyOn(mockEvent, 'preventDefault');

      component['onDropZoneDragOver'](mockEvent, 'left');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockEvent.dataTransfer!.dropEffect).toBe('move');
    });
  });
});
