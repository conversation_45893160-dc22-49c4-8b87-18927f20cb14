import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UnicaEmailEditorComponent } from './unica-email-editor.component';
import { CdkDragEnd } from '@angular/cdk/drag-drop';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('UnicaEmailEditorComponent', () => {
  let component: UnicaEmailEditorComponent;
  let fixture: ComponentFixture<UnicaEmailEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UnicaEmailEditorComponent, NoopAnimationsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(UnicaEmailEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default elementToolbarPosition as right', () => {
    expect(component.elementToolbarPosition).toBe('right');
  });

  it('should render drag handle in the drawer', () => {
    const compiled = fixture.nativeElement;
    const dragHandle = compiled.querySelector('.drawer-drag-handle');
    expect(dragHandle).toBeTruthy();
  });

  it('should render drag dots indicator', () => {
    const compiled = fixture.nativeElement;
    const dragDots = compiled.querySelector('.drag-dots');
    expect(dragDots).toBeTruthy();

    const dots = compiled.querySelectorAll('.drag-dots span');
    expect(dots.length).toBe(3);
  });

  describe('onDrawerDragEnded', () => {
    let mockDragEndEvent: Partial<CdkDragEnd>;
    let mockElement: any;
    let mockContainer: any;

    beforeEach(() => {
      mockContainer = {
        clientWidth: 1000
      };

      mockElement = {
        nativeElement: {
          closest: jasmine.createSpy('closest').and.returnValue(mockContainer)
        }
      };

      mockDragEndEvent = {
        distance: { x: 0, y: 0 },
        source: {
          element: mockElement,
          reset: jasmine.createSpy('reset')
        }
      };
    });

    it('should switch from left to right when dragged right beyond threshold', () => {
      component.elementToolbarPosition = 'left';
      mockDragEndEvent.distance!.x = 350; // 35% of 1000px container width

      component['onDrawerDragEnded'](mockDragEndEvent as CdkDragEnd);

      expect(component.elementToolbarPosition).toBe('right');
      expect(mockDragEndEvent.source!.reset).toHaveBeenCalled();
    });

    it('should switch from right to left when dragged left beyond threshold', () => {
      component.elementToolbarPosition = 'right';
      mockDragEndEvent.distance!.x = -350; // -35% of 1000px container width

      component['onDrawerDragEnded'](mockDragEndEvent as CdkDragEnd);

      expect(component.elementToolbarPosition).toBe('left');
      expect(mockDragEndEvent.source!.reset).toHaveBeenCalled();
    });

    it('should not switch position when drag distance is below threshold', () => {
      component.elementToolbarPosition = 'left';
      mockDragEndEvent.distance!.x = 200; // 20% of 1000px container width (below 30% threshold)

      component['onDrawerDragEnded'](mockDragEndEvent as CdkDragEnd);

      expect(component.elementToolbarPosition).toBe('left');
      expect(mockDragEndEvent.source!.reset).toHaveBeenCalled();
    });

    it('should not switch from left to right when dragged left', () => {
      component.elementToolbarPosition = 'left';
      mockDragEndEvent.distance!.x = -350; // Dragged left from left position

      component['onDrawerDragEnded'](mockDragEndEvent as CdkDragEnd);

      expect(component.elementToolbarPosition).toBe('left');
    });

    it('should not switch from right to left when dragged right', () => {
      component.elementToolbarPosition = 'right';
      mockDragEndEvent.distance!.x = 350; // Dragged right from right position

      component['onDrawerDragEnded'](mockDragEndEvent as CdkDragEnd);

      expect(component.elementToolbarPosition).toBe('right');
    });
  });
});
