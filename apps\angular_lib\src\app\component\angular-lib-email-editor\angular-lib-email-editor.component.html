<div class="email-editor-container">
  <div class="editor-toolbar">
    <div class="left-align" style="display: flex">
      <div class="subject-line-text-container">
        <unica-input [variant]="'default'" name="subjectLine" [label]="'Subject line'"></unica-input>
      </div>
    </div>

    <!--The center align toolbar elements-->
    <div class="center-align">
      <unica-button [variant]="'icon'"
                    *ngrxLet="canUndo$; let canUndo"
                    [disabled]="!canUndo"
                    (clickAction)="undo()"
                    [type]="'button'">
        <unica-icon
          [ngClass]="{'disabled': !canUndo}"
          [name]="'undo'"></unica-icon>
      </unica-button>
      <unica-button [variant]="'icon'"
                    *ngrxLet="canRedo$; let canRedo"
                    [disabled]="!canRedo"
                    (clickAction)="redo()"
                    [type]="'button'">
        <unica-icon
          [ngClass]="{'disabled': !canRedo}"
          [name]="'redo'"></unica-icon>
      </unica-button>
    </div>

    <!--The center align toolbar elements-->
    <div class="right-align">
      <unica-button
        (clickAction)="generateJson()"
        [type]="'button'"
        [width]="'180px'">
        Generate JSON
      </unica-button>
    </div>
  </div>
  <div class="editor-canvas-container">
    <!--<unica-email-editor-->
    <!--  [email]="emailDef"></unica-email-editor>-->
    <unica-email-editor
      (init)="canvasReady($event)"></unica-email-editor>
  </div>
</div>
