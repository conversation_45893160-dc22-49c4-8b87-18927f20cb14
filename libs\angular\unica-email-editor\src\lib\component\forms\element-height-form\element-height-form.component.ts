import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { UnicaNumberSpinnerComponent } from '@hcl/angular/unica-input';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { ElementWidthHeight } from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, tap } from 'rxjs';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ElementHeightFormService } from '../../../service/form/element-height-form.service';
import { UnicaSlideToggleComponent } from '@hcl/angular/unica-slide-toggle';

@UntilDestroy()
@Component({
  selector: 'element-height-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaNumberSpinnerComponent, UnicaTypographyComponent, UnicaSlideToggleComponent],
  providers: [ElementHeightFormService],
  templateUrl: './element-height-form.component.html',
  styleUrl: './element-height-form.component.scss',
})
export class ElementHeightFormComponent implements AfterViewInit {
  /**
   * Get the style of the current element
   */
  private elementStyle$ = this.canvasService.styleOfFocusedElement$
  /**
   * The Width
   */
  private _height: ElementWidthHeight | undefined;
  private heightSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected height$ = this.heightSubject.asObservable().pipe(tap(x => this._height = x));
  @Input() set height(v: ElementWidthHeight | undefined) {
    this.heightSubject.next(v);
    if (v) {
      if (v.auto === undefined) {
        v.auto = false
      }
      this.form.setValue(v);
    }
  }
  /**
   * Do we want the enable auto toggle
   */
  @Input() enableAuto = false;

  // the width field
  @ViewChild('height') input: UnicaNumberSpinnerComponent | undefined;
  /**
   * the width Form Group
   */
  protected readonly form: FormGroup;
  /**
   * Default constructor
   */
  constructor(private heightForm: ElementHeightFormService,
              private translate: TranslateService,
              private canvasService: EmailCanvasService) {
    this.form = this.heightForm.form;
    this.elementStyle$.pipe(untilDestroyed(this)).subscribe((s) => {
      this.height = s?.height;
    });
    // tell the canvas service to change
    this.heightForm.valueChange$.pipe(distinctUntilChanged((previous, current) => {
      if (previous && current &&
        previous.value === current.value &&
        previous.unit === current.unit &&
        previous.auto === current.auto)
        return true;
      return false;
    }),tap((v) => {
        const valForm = this.form.get('value');
        if (v && valForm) {
          if (v.auto) {
            // auto enabled, so disable the input
            valForm.disable({onlySelf: true, emitEvent: true});
          } else {
            // auto disabled so enable the input
            valForm.enable({onlySelf: true, emitEvent: true});
          }
        }
    }),
    untilDestroyed(this)).subscribe((v) => {
      this.canvasService.updatedFocusedElementStyle('height', { height: 
        {
          ...v, 
          // this had to be done to avoid the 'v.value' being undefined, in case of auto we are setting the value control to be disabled and thats the reason v.value is undefined when auto is disabled after being enabled once
          value: v?.value || this.form.get('value')?.value,
          unit: v?.unit || 'px'
        }
      });
    });
  }
  /**
   * When the view is ready we set the focus
   */
  ngAfterViewInit(): void {
    this.input?.focus()
  }

  updateHeightInForm(value: ElementWidthHeight) {
    if (this.form && value) {
      this.form.setValue(value);
    }
  }
  /**
   * Handle the errors on the fields
   * @protected
   */
  protected errorTranslator(field: string,error: ValidationErrors): string {
    if (error) {
      const errorList = Object.keys(error);
      if(errorList.length > 0) {
        // the 0th key represents the error
        return this.translate.instant('UNICA_COMMON.ERRORS.' + errorList[0].toUpperCase(),
          { field: this.translate.instant(field), ...error });
      }
    }
    return '';
  }
}
