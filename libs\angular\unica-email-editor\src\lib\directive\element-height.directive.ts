import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ElementWidthHeight } from '../config/email-common-elements';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { createWidthHeight } from '../helper/element-utils';

@UntilDestroy()
@Directive({
  selector: '[elementHeight]',
  standalone: true,
})
export class ElementHeightDirective {
  /**
   * The details of the font to be applied
   */
  private heightSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected height$ = this.heightSubject.asObservable();
  @Input() set elementHeight(w: ElementWidthHeight | null) {
    this.heightSubject.next(w === null ? undefined : w);
  }
  /**
   * The default constructor that has the element & the renderer to
   * be used to update the Width
   */
  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.height$.pipe(untilDestroyed(this)).subscribe((height) => {
      if (height) {
        this.update(height);
      }
    });
  }
  /**
   * This will update the style
   */
  private update(unicaHeight: ElementWidthHeight): void {
    if (unicaHeight) {
      const heightInPx = createWidthHeight(unicaHeight);
      if (heightInPx !== 'auto') {
        this.renderer.setStyle(this.el.nativeElement, 'height', heightInPx)
        // this.renderer.setStyle(this.el.nativeElement, 'flex', '1 1 ' + heightInPx)
      } else {
        this.renderer.setStyle(this.el.nativeElement, 'height', '100%')
      }
    }
  }
}
