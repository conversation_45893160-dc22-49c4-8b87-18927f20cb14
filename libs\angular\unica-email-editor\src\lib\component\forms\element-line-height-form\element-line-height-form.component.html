<div class="email-editor-form-container">
  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.LINE_HEIGHT' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field">
      <unica-number-spinner
        #lineHeight
        [name]="'value'"
        [errorTranslator]="errorTranslator.bind(this, 'EMAIL_EDITOR.LABELS.BORDER_WIDTH')"
        [form]="this.form.get('value')"></unica-number-spinner>
    </div>
  </div>

  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.UNIT' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field">
      <unica-dropdown #lineHeightUnit [label]="'Unit'" [form]="this.form.get('unit')" [required]="true" [placeholder]="'Place holder'"
          [options]="lineHeightUnitOptions">
        </unica-dropdown>
    </div>
  </div>
</div>
