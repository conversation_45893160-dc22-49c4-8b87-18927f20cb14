import { UnicaEmail } from '@hcl/angular/unica-email-editor';

/**
 * The default email for testing
 */
export const DEFAULT_EMAIL: UnicaEmail = {
  structures: [{
    type: 'cols_4',
    elements: [[{
      'label': 'labels.Image',
      'src': 'https://via.placeholder.com/600x200?text=CHANGE+ME',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 171,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 57,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>cdccds</p><p>cdscds</p><p>cdscdscds</p><p>cdscdscds</p><p>cdcdcsss</p><p>cdcd</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }], [{
      'label': 'labels.Image',
      'src': 'https://via.placeholder.com/600x200?text=CHANGE+ME',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 192,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 64,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>cdscds</p><p>cdcdcd</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }], [{
      'label': 'labels.Image',
      'src': 'https://via.placeholder.com/600x200?text=CHANGE+ME',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 171,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 57,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>cdscds</p><p>cd</p><p>ww</p><p>d</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }], [{
      'label': 'labels.Image',
      'src': 'https://via.placeholder.com/600x200?text=CHANGE+ME',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 171,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 57,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>cdscdscds</p><p>vf</p><p>aaaa</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }]],
    'columns': 4,
    'id': 1615446858809,
    // 'downloadDetails': null,
    'options': {
      'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
      'background': {
        'color': '#ffffff',
        'url': '',
        'repeat': 'repeat',
        'size': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      },
      'padding': {'top': 4, 'right': 4, 'bottom': 4, 'left': 4},
      'margin': {'top': 0, 'bottom': 0},
      'gaps': [4, 4],
      'height': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px', '%', 'cover', 'contain']},
      'columns': [{
        'background': {'color': '#b8dcee'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px', '%', 'cover', 'contain']}
      }, {
        'background': {'color': '#c0eccc'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px', '%', 'cover', 'contain']}
      }, {
        'background': {'color': '#f8efbf'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px', '%', 'cover', 'contain']}
      }, {
        'background': {'color': '#ecc4c4'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px', '%', 'cover', 'contain']}
      }],
      'columnsWidth': [2.5, 2.5, 2.5, 2.5]
    }
  }, {
    'type': 'cols_1',
    'elements': [[{
      'label': 'labels.Image',
      'src': 'https://images.unsplash.com/photo-1613066633868-34d0c5ccefdf?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxleHBsb3JlLWZlZWR8Mnx8fGVufDB8fHw%3D&auto=format&fit=crop&w=600&q=60',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 300,
        'width': {'value': 300, 'unit': 'px', 'auto': false, 'units': ['px']},
        'defaultHeight': 200,
        'height': {'value': 200, 'unit': 'px', 'auto': false, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'right',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>dedewdewdew</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }]],
    'columns': 1,
    'id': 1615481177448,
    // 'downloadDetails': null,
    'options': {
      'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
      'background': {
        'color': '#ffffff',
        'url': '',
        'repeat': 'repeat',
        'size': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      },
      'padding': {'top': 4, 'right': 4, 'bottom': 4, 'left': 4},
      'margin': {'top': 0, 'bottom': 0},
      'gaps': [4, 4],
      'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
      'columns': [{
        'background': {'color': 'transparent'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      }],
      'columnsWidth': [1]
    }
  }, {
    'type': 'cols_2',
    'elements': [[{
      'label': 'labels.Image',
      'src': 'https://images.unsplash.com/photo-1613066633868-34d0c5ccefdf?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxleHBsb3JlLWZlZWR8Mnx8fGVufDB8fHw%3D&auto=format&fit=crop&w=600&q=60',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 488,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 307,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>dedewdewdew</p><p>dewdewdew</p><p>dewdewdew</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }], [{
      'label': 'labels.Image',
      'src': 'https://images.unsplash.com/photo-1613066633868-34d0c5ccefdf?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxleHBsb3JlLWZlZWR8Mnx8fGVufDB8fHw%3D&auto=format&fit=crop&w=600&q=60',
      'state': {'disabled': false, 'message': ''},
      'type': 'image',
      'icon': 'image',
      'family': 'block-element',
      'downloadDetails': null,
      'options': {
        'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
        'defaultWidth': 488,
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'defaultHeight': 307,
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px']},
        'link': {'href': '', 'target': '_blank'},
        'align': 'center',
        'title': '',
        'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
      },
      'type_display': 'labels.Image'
    }, {
      'label': 'labels.Text',
      'innerText': '<p>dewdewdewdew</p>',
      'state': {'disabled': false, 'message': ''},
      'type': 'text',
      'icon': 'text_format',
      'family': 'block-element',
      'options': {
        'color': '#000000',
        'font': {'fallback': 'Arial, Helvetica, sans-serif', 'family': 'Roboto', 'style': 'normal', 'size': 16, 'weight': 400},
        'lineHeight': {'value': 40, 'unit': 'px'},
        'padding': {'top': 10, 'right': 25, 'bottom': 10, 'left': 25}
      },
      'type_display': 'labels.Text'
    }]],
    'columns': 2,
    'id': 1615481229600,
    // 'downloadDetails': null,
    'options': {
      'border': {'color': '#ffffff', 'style': 'solid', 'width': 0, 'radius': 0},
      'background': {
        'color': '#ffffff',
        'url': '',
        'repeat': 'repeat',
        'size': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      },
      'padding': {'top': 4, 'right': 4, 'bottom': 4, 'left': 4},
      'margin': {'top': 0, 'bottom': 0},
      'gaps': [4, 4],
      'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
      'columns': [{
        'background': {'color': 'transparent'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      }, {
        'background': {'color': 'transparent'},
        'border': {'width': 0, 'color': '#ffffff', 'radius': 0, 'style': 'solid'},
        'verticalAlign': 'top',
        'height': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']},
        'width': {'value': 100, 'unit': 'px', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
      }],
      'columnsWidth': [5, 5]
    }
  }],
  'forms': [],
  'general': {
    'previewText': '',
    'width': {'value': 1000, 'unit': 'px', 'units': ['px', '%']},
    'background': {
      'color': '#f1f1f1',
      'repeat': 'repeat',
      'size': {'value': 100, 'unit': '%', 'auto': true, 'units': ['px', '%', 'cover', 'contain']}
    },
    'padding': {'top': 16, 'right': 10, 'bottom': 10, 'left': 10},
    'direction': 'ltr',
    'global': {'fonts': [], 'padding': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}}
  }
};
