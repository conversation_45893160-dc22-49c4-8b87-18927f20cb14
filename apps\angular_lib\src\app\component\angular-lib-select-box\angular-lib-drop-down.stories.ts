import { UnicaDropdownComponent } from '@hcl/angular/unica-dropdown';
import {
  componentWrapperDecorator,
  Meta,
  StoryObj,
  applicationConfig,
} from '@storybook/angular';
import { provideAnimations } from '@angular/platform-browser/animations';
import { fn } from '@storybook/test';

export const ActionsData = {
  selectionChange: fn()
};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaDropdownComponent> = {
  title: 'Unica Drop-down',
  component: UnicaDropdownComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will be used to render a drop-down in unica application',
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [provideAnimations()],
    }),
    componentWrapperDecorator(
      (story) => `<div class="light-theme">${story}</div>`,
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    label: {
      description: 'This is the label that is associated with the drop-down',
      type: 'string',
    },
    placeholder: {
      type: 'string',
      description:
        'When the drop-down box is empty this value will be displayed as a place holder',
    },
    disabled: {
      type: 'boolean',
      description:
        'Based on this value the drop-down box will be enabled or disabled',
    },
    hint: {
      type: 'string',
      description:
        'This is a string that will be displayed below the drop-down box that will help user' +
        'to identify what this drop-down box represents.',
    },
  },
  args: {
    ...ActionsData
  },
};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaDropdownComponent>;
/**
 * The default input box
 */
export const Default: Story = {
  args: {
    label: 'Drop-down',
    hint: 'This is a sample Drop-down box',
    options: ['Str1', 'str2'],
    multiple: false,
    required:true,
    selectionChange: fn()
  },
};

export const StringOptions: Story = {
  args: {
    label: 'String Options',
    hint: 'This is a sample Drop-down box',
    options: ['Option 1', 'Option 2'],
    disabled: true,
    required:true,
  },
};

export const WithUnicaOptions: Story = {
  args: {
    label: 'String Options',
    hint: 'This is a sample Drop-down box',
    required:true,
    options: [
      {
        label: 'Opt 1',
        value: 'opt1',
      },
      {
        label: 'Opt 2',
        value: 'opt2',
      },
      {
        label: 'Opt 3',
        value: 'opt3',
      },
    ],

    disabled: true,
  },
};
