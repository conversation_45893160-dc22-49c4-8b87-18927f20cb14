import { AfterViewInit, ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { UnicaNumberSpinnerComponent } from '@hcl/angular/unica-input';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { ElementWidthFormService } from '../../../service/form/element-width-form.service';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { ElementWidthHeight } from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, tap } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UnicaSlideToggleComponent } from '@hcl/angular/unica-slide-toggle';

@UntilDestroy()
@Component({
  selector: 'element-width-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaNumberSpinnerComponent, UnicaTypographyComponent, UnicaSlideToggleComponent],
  providers: [ElementWidthFormService],
  templateUrl: './element-width-form.component.html',
  styleUrl: './element-width-form.component.scss',
  changeDetection:ChangeDetectionStrategy.OnPush
})
export class ElementWidthFormComponent  implements AfterViewInit {
  /**
   * Get the style of the current element
   */
  private elementStyle$ = this.canvasService.styleOfFocusedElement$
  /**
   * The Width
   */
  private _width: ElementWidthHeight | undefined;
  private widthSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected width$ = this.widthSubject.asObservable().pipe(tap(x => this._width = x));
  @Input() set width(v: ElementWidthHeight | undefined) {
    this.widthSubject.next(v);
    if (v) {
      if (v.auto === undefined) {
        v.auto = false
      }
      this.form.setValue(v);
    }
  }
  /**
   * Do we want the enable auto toggle
   */
  @Input() enableAuto = false;

  // the width field
  @ViewChild('width') input: UnicaNumberSpinnerComponent | undefined;
  /**
   * the width Form Group
   */
  protected readonly form: FormGroup;
  /**
   * Default constructor
   */
  constructor(private widthForm: ElementWidthFormService,
              private translate: TranslateService,
              private canvasService: EmailCanvasService) {
    this.form = this.widthForm.form;
    this.elementStyle$.pipe(untilDestroyed(this)).subscribe((s) => {
      this.width = s?.width;
    });
    // tell the canvas service to change
    this.widthForm.valueChange$.pipe(distinctUntilChanged((previous, current) => {
      if (previous && current &&
        previous.value === current.value &&
        previous.unit === current.unit &&
        previous.auto === current.auto)
        return true;
      return false;
    }),tap((v) => {
      const valForm = this.form.get('value');
      if (v && valForm) {
        if (v.auto) {
          // auto enabled, so disable the input
          valForm.disable({onlySelf: true, emitEvent: true});
        } else {
          // auto disabled so enable the input
          valForm.enable({onlySelf: true, emitEvent: true});
        }
      }
    }), untilDestroyed(this)).subscribe((v) => {
      this.canvasService.updatedFocusedElementStyle('width', { width: 
        {
          ...v, 
           // this had to be done to avoid the 'v.value' being undefined, in case of auto we are setting the value control to be disabled and thats the reason v.value is undefined when auto is disabled after being enabled once
          value: v?.value || this.form.get('value')?.value,
          unit: v?.unit || 'px'
        }
      });
    });
  }
  /**
   * When the view is ready we set the focus
   */
  ngAfterViewInit(): void {
    this.input?.focus()
  }

  updateWidthInForm(value: ElementWidthHeight | undefined): void {
    if (this.form && value) {
      this.form.setValue(value);
    }
  }
  /**
   * Handle the errors on the fields
   * @protected
   */
  protected errorTranslator(field: string,error: ValidationErrors): string {
    if (error) {
      const errorList = Object.keys(error);
      if(errorList.length > 0) {
        // the 0th key represents the error
        return this.translate.instant('UNICA_COMMON.ERRORS.' + errorList[0].toUpperCase(),
          { field: this.translate.instant(field), ...error });
      }
    }
    return '';
  }
}
