import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AngularLibDashboardBuilderComponent } from './angular-lib-dashboard-builder.component';

describe('AngularLibDashboardBuilderComponent', () => {
  let component: AngularLibDashboardBuilderComponent;
  let fixture: ComponentFixture<AngularLibDashboardBuilderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AngularLibDashboardBuilderComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(AngularLibDashboardBuilderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
