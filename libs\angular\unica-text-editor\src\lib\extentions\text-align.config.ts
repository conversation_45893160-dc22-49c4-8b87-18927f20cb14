import Quill, { Parchment } from 'quill';
import { StyleAttributor } from 'parchment';

export default function TextAlignQuillModule() {
  // Define a new StyleAttributor for text alignment
  const AlignStyle = new StyleAttributor('align', 'text-align', {
    scope: Parchment.Scope.BLOCK,
    whitelist: ['right', 'center', 'justify', 'left'],
  });

  // Register the new format under the same key to override the default
  Quill.register('formats/align', AlignStyle, true);
}
