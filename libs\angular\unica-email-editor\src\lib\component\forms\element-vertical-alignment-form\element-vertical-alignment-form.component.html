<div class="email-editor-form-container">
  <div class="email-editor-form-element">
    <!-- Vertical Alignment Text -->
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{ 'EMAIL_EDITOR.LABELS.VERTICAL_ALIGNMENT' | translate }}
      </unica-typography>
    </div>

    <ng-container>
      <!-- Vertical Alignment Toggle Group -->
      <div class="alignment-form-field">
        <unica-button-toggle-group
          [elements]="verticalAlignmentToggleElements"
          [form]="this.form.get('verticalAlign')"
        >
          <ng-template unicaTemplate [templateName]="'verticalAlignmentToggleButton'" let-element="element">
            <div class="vertical-alignment-toggle-icon" [title]="element.label">
              <unica-icon [name]="element.icon"></unica-icon>
            </div>
          </ng-template>
        </unica-button-toggle-group>
      </div>
    </ng-container>
  </div>
</div>