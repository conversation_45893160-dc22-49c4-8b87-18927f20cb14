import { Injectable } from '@angular/core';
import {
  ElementBackground,
  ElementBorder,
  ElementFont,
  ElementPaddingMargin, ElementStyle,
  ElementWidthHeight
} from '../config/email-common-elements';
import { combineLatest, map, Observable, of } from 'rxjs';
import {
  EmailGeneralOptions,
  UnicaEmail,
  UnicaStructure,
  UnicaStructureColumn,
  UnicaStructureOptions, UnicaTextBlock
} from '../config/email';
import { cloneDeep } from 'lodash-es';

/**
 * This is a service that will be referred
 * by the editor to get default values whenever required
 */
@Injectable({
  providedIn: 'root'
})
export class EmailDefaultService {
  /**
   * Return the default background that the email will have
   */
  public getEmailDefaultBackground(): Observable<ElementBackground> {
    return of({
      // url: '',
      color: '#ffffff',
      repeat: 'repeat',
      size: {
        value: 100,
        unit: '%',
        auto: true,
        units: ['px', '%', 'cover', 'contain']
      }
    });
  }
  /**
   * Return the default width of the email
   */
  public getEmailDefaultWidth(): Observable<ElementWidthHeight> {
    return of({
      value: 1000,
      unit: 'px',
      units: ['px', '%']
    })
  }
  /**
   * Return the default width of the email
   */
  public getEmailDefaultPadding(): Observable<ElementPaddingMargin> {
    return of( {
      top: 16,
      right: 10,
      bottom: 10,
      left: 10
    })
  }
  /**
   * Return the email general options
   */
  public getEmailGeneralOptions() : Observable<EmailGeneralOptions> {
    return combineLatest([
      this.getEmailDefaultBackground(),
      this.getEmailDefaultPadding(),
      this.getEmailDefaultWidth()
    ]).pipe(map( ([background, padding, width]) => {
      return {
        previewText: '',
        width: {...width},
        background: {...background},
        padding: {...padding},
        direction: 'ltr',
        global: {
          fonts: [],
          padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
          }
        }
      };
    }));
  }

  /**
   * Return the default email def
   */
  public getDefaultEmail(): Observable<UnicaEmail> {
    return this.getEmailGeneralOptions().pipe(
      map((o) => {
        return {
          version: '1.0',
          structures: [],
          general: {...o}
        };
      })
    )
  }
  /**
   * Get the default Structure Dimension
   */
  public getStructureDimension(): Observable<ElementWidthHeight> {
    return of({
      auto: true,
      value: 100,
      unit: 'px',
      units: ['px','%','cover','contain']
    });
  }

  /**
   * Def background of the structure
   */
  public getStructureBackground(): Observable<ElementBackground> {
    return this.getStructureDimension().pipe(
      map((dim) => {
        return {
          color: '#FFFFFF',
          url: '',
          repeat: 'repeat',
          size: {...dim}
        }
      })
    );
  }

  /**
   * Default border for the structure
   */
  public getStructureBorder(): Observable<ElementBorder> {
    return of({
      color:'#595959',
      style:'solid',
      width:0,
      radius:0
    });
  }
  /**
   * Default background of a column
   */
  public getStructureColumnBackground(): Observable<ElementBackground> {
    return of({
      color: '#e9f3f4'
    })
  }
  /**
   * Default background of a column
   */
  public getStructureColumnBorder(): Observable<ElementBorder> {
    return of({
      color:'#FFFFFF',
      style:'solid',
      width:0,
      radius:0
    })
  }
  /**
   * Def def of column
   */
  public getStructureColumn(): Observable<UnicaStructureColumn> {
    return combineLatest([
      this.getStructureColumnBackground(),
      this.getStructureColumnBorder(),
      this.getStructureDimension()
    ]).pipe(
      map(([bg, br, dim]) => {
        return {
          background: {...bg},
          border: {...br},
          height: {...dim},
          verticalAlign: 'top',
          width: {...dim}
        }
      })
    );
  }
  /**
   * Get teh Default Structure of 1 column Configuration
   */
  public getDefaultStructure(isDefaultDesigned = false): Observable<UnicaStructure> {
    return combineLatest([
      this.getStructureBackground(),
      this.getStructureBorder(),
      this.getStructureColumn(),
      this.getStructureDimension()
    ]).pipe(
      map(([bg, border, col, dim]) => {
        return {
          type: 'cols_1',
          id: 1,
          columns: 1,
          elements: [],
          options: {
            background: cloneDeep(bg),
            border: { ...border },
            columns: [{...col}],
            columnsWidth: [1],
            gaps: [4, 4],
            height: {...dim},
            isDefaultDesigned: isDefaultDesigned,
            margin: {top: 0, bottom: 0, right: 0, left: 0},
            padding: {
              top: 4,
              bottom: 4,
              left: 4,
              right: 4,
            }
          }
        }
      })
    );
  }
  /**
   * Return the System default Structures
   */
  public getSystemDefaultStructures(): Observable<UnicaStructure[]> {
    return combineLatest([
      this.getDefaultStructure(),
      this.getStructureColumn(),
    ]).pipe(
      map(([col1, colDef]) => {
        const cols2Options: UnicaStructureOptions = {...col1.options, columnsWidth: [5,5], columns: [{...colDef},{...colDef}]}
        const cols2: UnicaStructure = {...col1, options: cols2Options, columns: 2, id: 2, type: 'cols_2'};

        const cols3Options: UnicaStructureOptions = {...col1.options, columnsWidth: [3.33,3.33,3.33], columns: [{...colDef},{...colDef},{...colDef}]}
        const cols3: UnicaStructure = {...col1, options: cols3Options, columns: 3, id: 3, type: 'cols_3'};

        const cols4Options: UnicaStructureOptions = {...col1.options, columnsWidth: [2.5,2.5,2.5,2.5], columns: [{...colDef},{...colDef},{...colDef},{...colDef}]}
        const cols4: UnicaStructure = {...col1, options: cols4Options, columns: 4, id: 4, type: 'cols_4'};

        const cols70And30Options: UnicaStructureOptions = {...col1.options, columnsWidth: [7, 3], columns: [{...colDef},{...colDef}]}
        const cols70And30: UnicaStructure = {...col1, options: cols70And30Options, columns: 2, id: 5, type: 'cols_12'};

        const cols30And70Options: UnicaStructureOptions = {...col1.options, columnsWidth: [3, 7], columns: [{...colDef},{...colDef}]}
        const cols30And70: UnicaStructure = {...col1, options: cols30And70Options, columns: 2, id: 6, type: 'cols_21'};

        return [
          {...col1},
          cols2,
          cols3,
          cols4,
          cols70And30,
          cols30And70
        ]
      })
    );
  }

  /**
   * Teh styling for the empty image placeholder
   */
  public getEmptyImagePlaceHolder(): Observable<ElementStyle> {
    return of ({
      width: {unit: '%', value: 100},
      height: {unit: '%', value: 100},
      lineHeight: {value : 200, unit: 'px'},
      background: { color: '#cccccc'},
    })
  }

  public getDefaultFont(): Observable<ElementFont> {
    return of({
        label: 'Roboto',
        fallback: 'Arial, Helvetica, sans-serif',
        family: 'Roboto',
        style: 'normal',
        size: 16,
        weight: 400
      });
  }

  public getSystemDefaultText(): Observable<UnicaTextBlock[]> {
    const textBlocksConfig = [
      { label: 'Add Heading 1', size: 36, lineHeight: 50 },
      { label: 'Add Heading 2', size: 32, lineHeight: 48 },
      { label: 'Add Heading 3', size: 25, lineHeight: 40 },
      { label: 'Add Heading 4', size: 21, lineHeight: 36 },
      { label: 'Add Heading 5', size: 18, lineHeight: 32 },
      { label: `I'm a paragraph. Click here to add your own text.`, size: 16, lineHeight: 26 },
      { label: `I'm a paragraph. Click here to add your own text.`, size: 14, lineHeight: 22 },
    ];
    return this.getDefaultFont().pipe(
      map((defaultFont) => {
        return textBlocksConfig.map((config) => ({
          type: 'text',
          icon: 'text_format',
          family: 'block-element',
          label: config.label,
          innerText: '',
          type_display: '',
          options: {
            color: '#000000',
            font: {
              ...defaultFont,
              size: config.size,
              weight: 400,
              style: 'normal',
            },
            lineHeight: {
              value: config.lineHeight,
              unit: 'px'
            },
            padding: {
              top: 10,
              right: 25,
              bottom: 10,
              left: 25
            }
          },
          state: {
            disabled: false,
            message: ''
          }
        }));
      })
    );
  }

}
