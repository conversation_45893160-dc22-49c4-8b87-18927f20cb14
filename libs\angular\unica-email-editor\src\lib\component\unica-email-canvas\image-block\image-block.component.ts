import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import { EmailDefaultService } from '../../../service/email-default.service';
import { LetDirective } from '@ngrx/component';
import { ElementWidthDirective } from '../../../directive/element-width.directive';
import {
  DELETE,
  IMAGE_SETTINGS,
  PLACEHOLDER_IMAGE_URL,
  LINK
} from '../../../helper/email-editor.constant';
import { ElementHeightDirective } from '../../../directive/element-height.directive';
import { ElementLineHeightDirective } from '../../../directive/element-line-height.directive';
import { ElementBackgroundDirective } from '../../../directive/element-background.directive';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { FocusableEmailElement } from '../../../config/email-element-drop-list';
import {
  Align,
  ElementAttribute,
  ElementBorder,
  ElementPaddingMargin,
  ElementStyle,
  ElementWidthHeight,
  HideOnType,
  IDownloadDetailInfo,
  IRule,
  StyleAttribute,
  AddLinkFormDataConfig
} from '../../../config/email-common-elements';
import { GenerateUniqueId } from '@hcl/unica-common';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { EmailElementType } from '../../../config/element-toolbar';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { EmailBlock, UnicaImageBlock } from '../../../config/email';
import { BlockActionsService } from '../../../service/block-actions.service';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { TranslateService } from '@ngx-translate/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { AddLinkPopoverFormComponent } from '../../forms/add-link-popover-form/add-link-popover-form.component';
import { UnicaPopoverService } from '@hcl/angular/unica-dialog';
import { ElementBorderDirective } from '../../../directive/element-border.directive';
import { ElementPaddingDirective } from '../../../directive/element-padding.directive';
import { ElementAlignDirective } from '../../../directive/element-align.directive';

@UntilDestroy()
@Component({
  selector: 'image-block',
  standalone: true,
  imports: [
    CommonModule,
    LetDirective,
    ElementWidthDirective,
    ElementHeightDirective,
    ElementLineHeightDirective,
    ElementBackgroundDirective,
    ElementBorderDirective,
    ElementPaddingDirective,
    ElementAlignDirective,
    FocusOverlayComponent,
    UnicaButtonModule,
  ],
  templateUrl: './image-block.component.html',
  styleUrl: './image-block.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageBlockComponent
  implements FocusableEmailElement, AfterViewInit
{
  /**
   * The unique id
   */
  private _id: string = GenerateUniqueId(13);
  private _dropListId = '';
  private _blockIndex = 0;
  private addLinkFormData!: AddLinkFormDataConfig;

  get id(): string {
    return this._dropListId
      ? `${this._dropListId}_${this._blockIndex}`
      : this._id;
  }

  @Input() set dropListId(value: string) {
    this._dropListId = value;
  }

  @Input() set blockIndex(value: number) {
    this._blockIndex = value;
  }
  /**
   * The input for the block
   */
  private _block = new UnicaImageBlock();
  private blockSubject = new BehaviorSubject<UnicaImageBlock>(this._block);
  protected block$ = this.blockSubject
    .asObservable()
    .pipe(tap((b) => (this._block = b)));
  @Input({
    transform: (value: EmailBlock): UnicaImageBlock => <UnicaImageBlock>value,
  })
  set block(b: UnicaImageBlock) {
    this.blockSubject.next(b);
    this.imageSourceSubject.next(b.src || PLACEHOLDER_IMAGE_URL);
    this.heightSubject.next(b.options.height);
    this.widthSubject.next(b.options.width);
    this.downloadDetailsSubject.next(b.downloadDetails);
    this.borderSubject.next(b.options.border);
    this.paddingSubject.next(b.options.padding);
    this.hideOnSubject.next(b.options.hideOn);
    this.alignSubject.next(b.options.align);
    this.rulesSubject.next(b.options.rules);
  }
  /**
   * The style
   * @protected
   */
  protected style$ = this.block$.pipe(
    map((b) => {
      if (b) {
        return b.options;
      }
      return undefined;
    }),
  );

  /**
   * For canvas, Whe it is empty we have below actions
   */
  protected imageToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: IMAGE_SETTINGS,
      icon: 'settings',
      label: 'Settings',
    },
    {
      id: 'duplicate',
      icon: 'content_copy',
      label: 'Duplicate',
    },
    {
      id: LINK,
      icon: 'link',
      label: 'link'
    },
    {
      id: 'dynamic',
      icon: 'close',
      label: 'add dynamic content',
    },
    {
      id: DELETE,
      icon: 'delete',
      label: 'Delete',
    },
  ];
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged()
  );
  /**
   *
   * @param defaultService
   */
  constructor(
    private defaultService: EmailDefaultService,
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private blockActionsService: BlockActionsService,
    private dialogService: UnicaDialogService,
    private translate: TranslateService,
    private popoverService: UnicaPopoverService
  ) {}
  /**
   * Set the focus on canvas
   */
  public focus(event: Event | undefined): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   * Called when user wants to select a image
   */
  protected openImagePanel() {
    this.toolbarService.openPanel(EmailElementType.IMAGE_SETTINGS);
  }
  /**
   * Called when a element in the toolbar is selected by the user
   */
  protected performAction({
    item,
    event,
  }: {
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }) {
    switch (item.id) {
      default:
      case IMAGE_SETTINGS:
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.IMAGE_SETTINGS);
        break;
      case LINK:
        // fire event to open the link popover
        this.openLinkPopover(event);
        break;
      case 'duplicate': {
        this.blockActionsService.duplicateBlock(this);
        break;
      }
      case 'delete': {
        this.deleteBlock();
        break;
      }
      case 'dynamic': {
        this.canvasService.openRuleBuilder({ currentElement: this._block })
      }
    }
  }

  /**
   * Opens a popover to allow the user to update the link for the selected block.
   * 
   * This method is triggered by a user interaction event (either a mouse click or keyboard event)
   * and opens a popover where the user can enter or modify the URL associated with
   * the specific block.
   * 
   * @param event - The event to trigger the popover. It can either be a MouseEvent (click) or
   *                a KeyboardEvent.
   */
  openLinkPopover(event: MouseEvent | KeyboardEvent) {
    if (event instanceof KeyboardEvent && !['Enter', ' '].includes(event.key)) {
      return;
    }
    event.preventDefault();
    const targetElement = event.currentTarget as HTMLElement;
    const popoverInstance =
          this.popoverService.openPopover<AddLinkPopoverFormComponent>(
            AddLinkPopoverFormComponent,
            targetElement,
            {
              // Pass the entire saved object for prepopulating the form
              initialData: this.addLinkFormData ?? {
                redirection: 'url', // default values if none saved
                url: '',
                landingPage: '',
                aliasNameInfo: { name: '', id: '' },
                newWindow: false
              },
              blockName: 'Image',
            },
          );
    if (popoverInstance) {
      popoverInstance.addLinkFormData.pipe(untilDestroyed(this)).subscribe((addLinkFormData: AddLinkFormDataConfig) => {
        // Update your Block JSON here
        if (this._block?.options) {
          this._block.options.redirection = addLinkFormData.redirection;
          this._block.options.url = addLinkFormData.url;
          this._block.options.newWindow = addLinkFormData.newWindow;
          this._block.options.aliasNameInfo = addLinkFormData.aliasNameInfo;
          this._block.options.landingPage = addLinkFormData.redirection === 'lp' ? addLinkFormData.landingPage : '';

          this.addLinkFormData = addLinkFormData;
        }
        this.popoverService.closePopover();
      });
      popoverInstance.closePopover.pipe(untilDestroyed(this)).subscribe(() => {
        this.popoverService.closePopover();
      });
    }
  }

  private async deleteBlock() {
    const confirmed = await this.dialogService.confirm(
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.BLOCK_DELETION_CONFIRMATION'),
      this.translate.instant('MODAL.YES'),
      this.translate.instant('MODAL.NO'),
    );

    if (confirmed) {
      this.blockActionsService.deleteBlock(this);
    }
  }

  /**
   * Get the style of this image
   */
  getStyle(): ElementStyle {
    return this._block.options;
  }
  /**
   * Update the style of the image block
   */
  updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'border' && value && value.border) {
      this.borderSubject.next(<ElementBorder>value.border);
    } else if (attribute === 'padding' && value && value.padding) {
      this.paddingSubject.next(<ElementPaddingMargin>value.padding);
    } else if (attribute === 'hideOn' && value && value.hideOn !== undefined) {
      // we are checking for undefined because hideOn can be empty string also
      this.hideOnSubject.next(<HideOnType>value.hideOn);
    } else if (attribute === 'align' && value && value.align) {
      this.alignSubject.next(<Align>value.align);
    } else if (attribute === 'width' && value && value.width) {
      this.widthSubject.next(<ElementWidthHeight>value.width);
    } else if (attribute === 'height' && value && value.height) {
      this.heightSubject.next(<ElementWidthHeight>value.height);
    }
  }
  /**
   * Once the elements is dropped we will set the focus on this
   */
  ngAfterViewInit(): void {
    setTimeout(() => this.canvasService.setFocus(this), 100);
  }

  getElementAttribute(): ElementAttribute | undefined {
    return {
      rules: this._block.options.rules || [],
      src: this._block.src,
      downloadDetails: this._block.downloadDetails,
    };
  }

  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {
    if (attribute === 'rules' && value && value.rules !== undefined) {
      // we are checking for undefined because rules can be empty array
      this.rulesSubject.next(value.rules);
    } else if (attribute === 'src' && value && value.src !== undefined) {
      // we are checking for undefined because rules can be empty array
      this.imageSourceSubject.next(value.src);
    } else if (attribute === 'downloadDetails' && value && value.downloadDetails !== undefined) {
      this.downloadDetailsSubject.next(value.downloadDetails);
    }
  }

  /*
     * The hide On of this block
     */
    private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
      undefined,
    );
    protected hideOnSubject$ = this.hideOnSubject.asObservable().pipe(
      tap((w) => {
        if (this._block?.options) {
          this._block.options.hideOn = w || undefined;
        }
      }),
    );
  
    
  /**
     * The border of this block
     */
    private borderSubject = new BehaviorSubject<ElementBorder | undefined>(
      undefined,
    );
    protected borderSubject$ = this.borderSubject.asObservable().pipe(
      tap((w) => {
        if (this._block?.options) {
          this._block.options.border = w;
        }
      }),
    );
  
    /**
     * The padding of this block
     */
    private paddingSubject = new BehaviorSubject<
      ElementPaddingMargin | undefined
    >(undefined);
    protected paddingSubject$ = this.paddingSubject.asObservable().pipe(
      tap((w) => {
        if (this._block?.options) {
          this._block.options.padding = w;
        }
      }),
    );

  /**
   * The alignment of this block
   */
  private alignSubject = new BehaviorSubject<Align | undefined>(undefined);
  protected alignSubject$ = this.alignSubject.asObservable().pipe(
    tap((align) => {
      if (this._block?.options) {
        this._block.options.align = align;
      }
    }),
  );

  /**
   * * The rules of this block
   */
  private rulesSubject = new BehaviorSubject<IRule[] | undefined>(undefined);
  protected rulesSubject$ = this.rulesSubject.asObservable()
    .pipe(
      tap((rules) => {
        if (this._block?.options) {
          this._block.options.rules = rules;
        }
      })
    );
  
  /**
   * * The image source of this block
   */
  private imageSourceSubject = new BehaviorSubject<string | undefined>(undefined);
  protected imageSourceSubject$ = this.imageSourceSubject.asObservable()
    .pipe(
      tap((src) => {
        if (this._block) {
          this._block.src = src || PLACEHOLDER_IMAGE_URL;
        }
      })
    );

  /**
   * * The width of the image source of this block
   */
  private widthSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected widthSubject$ = this.widthSubject.asObservable()
    .pipe(
      tap((w) => {
        if (this._block?.options) {
          this._block.options.width = w;
        }
      })
    );

  /**
   * * The height of the image source of this block
   */
  private heightSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected heightSubject$ = this.heightSubject.asObservable()
    .pipe(
      tap((h) => {
        if (this._block?.options) {
          this._block.options.height = h;
        }
      })
    );

  /**
   * * The download details of an internal image source of this block
   */
  private downloadDetailsSubject = new BehaviorSubject<{
    // the Id of the asset that we want to download
    assetId?: number | string,
    // the id that needs to be given to the tag
    id?: string,
    // the URL which needs to be hit to download the asset
    assetUrl?: string,
    // flag thatr tells the current status of the asset
    assetDownloaded?: boolean
  } | IDownloadDetailInfo | null>(null);
  protected downloadDetailsSubject$ = this.downloadDetailsSubject.asObservable()
    .pipe(
      tap((downloadDetail) => {
        if (this._block) {
          this._block.downloadDetails = downloadDetail;
        }
      })
    ).subscribe(); // added subscribe as this behaviour subject is not used in the template, but we need to ensure that the value is updated in the block
  /**
   * The empty image Style
   * @protected
   */
  protected emptyImageStyle$ = this.defaultService.getEmptyImagePlaceHolder();
  /**
   * This tells if this is empty
   */
  protected isEmpty$ = this.imageSourceSubject$.pipe(
    map((src) => {
      return !src || src === PLACEHOLDER_IMAGE_URL;
    }),
    distinctUntilChanged()
  );
  /**
   * method to return button block as EmailBlock
   * This is used to get the block in the email canvas
   * @returns _block as EmailBlock
   */
  getCurrentBlock(): EmailBlock | undefined {
    return  this._block;
  }
}
