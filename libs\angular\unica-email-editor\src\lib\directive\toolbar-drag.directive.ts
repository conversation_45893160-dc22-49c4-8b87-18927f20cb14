import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  OnDestroy,
} from '@angular/core';

@Directive({
  selector: '[toolbarDrag]',
  exportAs: 'toolbarDrag',
  standalone: true,
})
export class ToolbarDragDirective implements OnDestroy {
  /**
   * Whether the toolbar drag is enabled
   */
  @Input() toolbarDragEnabled = false;

  /**
   * Current position of the toolbar
   */
  @Input() toolbarPosition: 'left' | 'right' = 'right';

  /**
   * Event emitted when drag state changes
   */
  @Output() dragStateChange = new EventEmitter<boolean>();

  /**
   * Event emitted when position should change
   */
  @Output() positionChange = new EventEmitter<'left' | 'right'>();

  /**
   * Mouse drag tracking
   */
  private isDragging = false;
  private startX = 0;
  private dragThreshold = 10; // pixels to move before starting drag
  private dragImage: HTMLElement | null = null;
  private currentDropZone: 'left' | 'right' | null = null;

  constructor(private elementRef: ElementRef<HTMLElement>) {}

  /**
   * Handle mouse down on drag handle - start of reliable drag system
   */
  onMouseDown(event: MouseEvent): void {
    if (!this.toolbarDragEnabled) return;

    console.log('Mouse down on drag handle');

    // Prevent CDK drag from interfering
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Record starting position
    this.startX = event.clientX;
    this.isDragging = false;

    // Add global mouse event listeners
    const onMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - this.startX;

      // Check if we've moved enough to start dragging
      if (!this.isDragging && Math.abs(deltaX) > this.dragThreshold) {
        this.startDrag();
      }

      if (this.isDragging) {
        // Update visual feedback and drag image position
        this.updateDragFeedback(deltaX, moveEvent.clientX, moveEvent.clientY);
      }
    };

    const onMouseUp = () => {
      if (this.isDragging) {
        this.endDrag();
      }

      // Clean up event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // Add global event listeners
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  /**
   * Start the drag operation
   */
  private startDrag(): void {
    console.log('Starting drag operation');
    this.isDragging = true;

    // Create drag image
    this.createDragImage();

    // Add visual feedback to the toolbar
    const toolbarContainer = this.elementRef.nativeElement.closest(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (toolbarContainer) {
      toolbarContainer.style.opacity = '0.4';
      toolbarContainer.style.transform = 'scale(0.95)';
    }

    // Notify parent that dragging started
    this.dragStateChange.emit(true);
  }

  /**
   * Create a drag image that follows the cursor
   */
  private createDragImage(): void {
    const toolbarContainer = this.elementRef.nativeElement.closest(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (!toolbarContainer) return;

    // Clone the toolbar container
    this.dragImage = toolbarContainer.cloneNode(true) as HTMLElement;

    // Style the drag image
    this.dragImage.style.position = 'fixed';
    this.dragImage.style.top = '50%';
    this.dragImage.style.left = '50%';
    this.dragImage.style.transform = 'translate(-50%, -10%) scale(1.05)';
    this.dragImage.style.opacity = '0.9';
    this.dragImage.style.pointerEvents = 'none';
    this.dragImage.style.zIndex = '99999';
    this.dragImage.style.background = 'rgba(255, 255, 255, 0.95)';
    this.dragImage.style.borderRadius = '8px';
    this.dragImage.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
    this.dragImage.style.border = '2px solid var(--unica-primary, #038D99)';
    this.dragImage.style.transition = 'none';
    this.dragImage.style.height = 'auto';

    // Remove drag handles from the clone
    const dragHandles = this.dragImage.querySelectorAll('.toolbar-drag-handle');
    dragHandles.forEach((handle) => handle.remove());

    // Add to document
    document.body.appendChild(this.dragImage);
  }

  /**
   * Update drag feedback based on movement
   */
  private updateDragFeedback(
    _deltaX: number,
    mouseX: number,
    mouseY: number,
  ): void {
    // Update drag image position to follow cursor
    if (this.dragImage) {
      this.dragImage.style.left = `${mouseX}px`;
      this.dragImage.style.top = `${mouseY}px`;
      this.dragImage.style.transform = 'translate(-50%, -10%) scale(1.05)';
    }

    // Check which drop zone we're over
    const newDropZone = this.getDropZoneAtPosition(mouseX, mouseY);

    // Update visual feedback if drop zone changed
    if (newDropZone !== this.currentDropZone) {
      this.updateDropZoneHighlight(this.currentDropZone, newDropZone);
      this.currentDropZone = newDropZone;
    }
  }

  /**
   * Determine which drop zone is at the given position
   */
  private getDropZoneAtPosition(x: number, y: number): 'left' | 'right' | null {
    const leftDropZone = document.querySelector(
      '.drop-zone-left',
    ) as HTMLElement;
    const rightDropZone = document.querySelector(
      '.drop-zone-right',
    ) as HTMLElement;

    if (leftDropZone && this.isElementVisible(leftDropZone)) {
      const rect = leftDropZone.getBoundingClientRect();
      if (
        x >= rect.left &&
        x <= rect.right &&
        y >= rect.top &&
        y <= rect.bottom
      ) {
        return 'left';
      }
    }

    if (rightDropZone && this.isElementVisible(rightDropZone)) {
      const rect = rightDropZone.getBoundingClientRect();
      if (
        x >= rect.left &&
        x <= rect.right &&
        y >= rect.top &&
        y <= rect.bottom
      ) {
        return 'right';
      }
    }

    return null;
  }

  /**
   * Check if an element is visible (has the 'visible' class)
   */
  private isElementVisible(element: HTMLElement): boolean {
    return element.classList.contains('visible');
  }

  /**
   * Update drop zone highlighting
   */
  private updateDropZoneHighlight(
    oldZone: 'left' | 'right' | null,
    newZone: 'left' | 'right' | null,
  ): void {
    // Remove highlight from old zone
    if (oldZone) {
      const oldElement = document.querySelector(
        `.drop-zone-${oldZone}`,
      ) as HTMLElement;
      if (oldElement) {
        oldElement.classList.remove('hover');
      }
    }

    // Add highlight to new zone
    if (newZone) {
      const newElement = document.querySelector(
        `.drop-zone-${newZone}`,
      ) as HTMLElement;
      if (newElement) {
        newElement.classList.add('hover');
      }
    }
  }

  /**
   * End the drag operation and determine new position
   */
  private endDrag(): void {
    console.log('Ending drag operation, drop zone:', this.currentDropZone);
    this.isDragging = false;

    // Clean up drag image
    if (this.dragImage && document.body.contains(this.dragImage)) {
      document.body.removeChild(this.dragImage);
      this.dragImage = null;
    }

    // Reset visual feedback
    const toolbarContainer = this.elementRef.nativeElement.closest(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (toolbarContainer) {
      toolbarContainer.style.opacity = '1';
      toolbarContainer.style.transform = 'none';
    }

    // Only change position if dropped on a valid drop zone
    if (this.currentDropZone && this.currentDropZone !== this.toolbarPosition) {
      console.log(
        'Valid drop detected, changing position from',
        this.toolbarPosition,
        'to',
        this.currentDropZone,
      );
      this.positionChange.emit(this.currentDropZone);
    } else {
      console.log('Invalid drop or same position, no change');
    }

    // Reset drop zone tracking
    this.currentDropZone = null;

    // Notify parent that dragging ended
    this.dragStateChange.emit(false);
  }

  /**
   * Cleanup on destroy
   */
  ngOnDestroy(): void {
    // Clean up any remaining drag image
    if (this.dragImage && document.body.contains(this.dragImage)) {
      document.body.removeChild(this.dragImage);
    }
  }
}
