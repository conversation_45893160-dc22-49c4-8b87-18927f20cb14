import { componentWrapperDecorator, Meta, StoryObj } from '@storybook/angular';
import { UnicaInputComponent } from '@hcl/angular/unica-input';

export const ActionsData = {};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaInputComponent> = {
  title: 'Unica Input',
  component: UnicaInputComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will be used to render the Input box across HCL Unica application',
      },
    },
  },
  decorators: [componentWrapperDecorator((story) => `<div class="light-theme">${story}</div>`)],

  tags: ['autodocs'],
  argTypes: {
    type: {
      type: 'string',
      control: {
        type: 'select',
      },
      options: ['text', 'password', 'number'],
      description:
        'This value determines the type of input, it can be "text","number","password"',
    },
    label: {
      type: 'string',
      description:
        'The label that we need to display for the text-box this needs to be a translated label',
    },
    placeholder: {
      type: 'string',
      description:
        'When the input box is empty this value will be displayed as a place holder',
    },
    name: {
      type: 'string',
      description: 'A name for the input box',
    },
    clearable: {
      description:
        'setting this flag true will add a \'x\' icon at teh end of the input ' +
        'box that will help to clear the value that is set in the input box.'
    },
    hint: {
      type: 'string',
      description:
        'This is a string that will be displayed belo the input box that will help user' +
        'to identify what this text box represents.',
    },
    prefixIcon: {
      control: {
        type: 'text'
      },
      description: 'The input box can contain a icon that is added before the input, this variable we can assign the icon'
    },
    postfixIcon: {
      control: {
        type: 'text'
      },
      description: 'The input box can contain a icon that is added at the end of input, this variable we can assign the icon'
    },
    disabled: {
      description:
        'Based on this value the input box will be enabled or disabled',
    },
    errorTranslator: {
      type: 'function',
      description:
        'We will use Reactive forms & these forms will have some validations associated' +
        'to them, in these cases in case there are some errors that need to be displayed' +
        'this component will call this function & send the error as param to the function, the' +
        'function needs to return the translated error message that needs to be displayed' +
        'as error on the input box."(err: ValidationError) => string"',
    },
    form: {
      type: 'function',
      description: 'The form control for the input box'
    }
  },
  args: {
    type: 'text',
  },
};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaInputComponent>;

/**
 * The default input box
 */
export const Default: Story = {
  args: {
    type: 'text',
    label: 'Label',
    name: 'unicaInput',
    disabled: false,
    clearable: false
  },
}
