import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ElementWidthHeight } from '../config/email-common-elements';
import { createWidthHeight } from '../helper/element-utils';

/**
 * This is a directive that will update the element width of
 * the dom node based on the "ElementWidthHeight"
 */
@UntilDestroy()
@Directive({
  standalone: true,
  selector: '[elementWidth]'
})
export class ElementWidthDirective {
  /**
   * The details of the font to be applied
   */
  private widthSubject = new BehaviorSubject<ElementWidthHeight | undefined>(undefined);
  protected width$ = this.widthSubject.asObservable();
  @Input() set elementWidth(w: ElementWidthHeight | null) {
    this.widthSubject.next(w === null ? undefined : w);
  }
  /**
   * The default constructor that has the element & the renderer to
   * be used to update the Width
   */
  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.width$.pipe(untilDestroyed(this)).subscribe((width) => {
      if (width) {
        this.update(width);
      }
    });
  }
  /**
   * This will update the style
   */
  private update(unicaWidth: ElementWidthHeight): void {
    if (unicaWidth) {
      const widthInPx = createWidthHeight(unicaWidth);
      this.renderer.setStyle(this.el.nativeElement, 'max-width', '100%')
      if (widthInPx !== 'auto') {
        this.renderer.setStyle(this.el.nativeElement, 'width', widthInPx)
      } else {
        this.renderer.setStyle(this.el.nativeElement, 'width', '100%')
      }
    }
  }
}
