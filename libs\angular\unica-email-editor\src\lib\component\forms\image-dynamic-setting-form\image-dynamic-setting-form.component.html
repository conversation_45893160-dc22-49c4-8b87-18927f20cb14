<div class="form-group image-dynamic-setting-form">
    <div *ngrxLet="elementAttribute$; let attribute">
        <ng-container *ngIf="attribute?.rules?.length || 0">
            <ng-container *ngTemplateOutlet="ruleTemplate; context: { $implicit: getDefaultRuleInfo() }">
            </ng-container>
            <unica-typography [variant]="'text'">
                {{ 'EMAIL_EDITOR.LABELS.DYNAMIC_CONTENT' | translate }} ({{ attribute?.rules?.length || 0 }})
            </unica-typography>
            @for (ruleInfo of attribute?.rules; track ruleInfo; let i = $index) {
                <!-- Use ngTemplateOutlet to render the rule template -->
                <ng-container *ngTemplateOutlet="ruleTemplate; context: { $implicit: ruleInfo, index: i }">
                </ng-container>
            }
        </ng-container>
        <ng-container> <!--*ngIf="!isDefaultImageSrcPresent()" - commenting for now, uncomment after aset picker integration-->
            <div class="add-dynamic-button-container">
                <unica-button
                    (clickAction)="addRulesToImage()"
                    [type]="'button'"
                    [width]="'180px'">
                    {{ 'EMAIL_EDITOR.LABELS.ADD_DYNAMIC_CONTENT' | translate }}
                </unica-button>
            </div>
        </ng-container>
        <ng-container *ngIf="false"> <!--*ngIf="isDefaultImageSrcPresent()" - commenting for now, uncomment after aset picker integration-->
            <unica-typography [variant]="'text'">
                {{ 'EMAIL_EDITOR.LABELS.ADD_DEFAULT_IMAGE_SRC' | translate }}
            </unica-typography>
        </ng-container>
    </div>
    <!-- Define the reusable template -->
    <ng-template #ruleTemplate let-ruleInfo let-i="index">
        <div class="rule-info-container mtop-3" [ngClass]="ruleInfo.isDefault ? 'full-width' : 'w-80'">
        <!-- Loader -->
        <div *ngIf="isLoading" class="float-left imgalignment">
            <div class="loaderdiv"></div>
            <unica-typography [variant]="'text'">
            {{ 'EMAIL_EDITOR.LABELS.LOADING_PLEASE_WAIT' | translate }}
            </unica-typography>
        </div>

        <!-- Image -->
        <div class="float-left imgalignment">
            <img src="{{ ruleInfo.imageSrc }}" (load)="isLoading = false;" />
        </div>

        <!-- Rule Name -->
        <div class="rule-name">
            <unica-typography [variant]="'text'">{{ ruleInfo.name }}</unica-typography>
        </div>
        </div>

        <!-- Actions -->
        <div class="actions-container pt-2 pb-2" *ngIf="!ruleInfo.isDefault">
            <div class="edit-delete-icon">
                <unica-icon
                [name]="'edit'"
                (click)="actionClicked('edit', i);"
                class="mr-10">
                </unica-icon>
            </div>
            <div class="edit-delete-icon">
                <unica-icon
                [name]="'delete'"
                (click)="actionClicked('delete', i);">
                </unica-icon>
            </div>
        </div>
    </ng-template>

</div>
