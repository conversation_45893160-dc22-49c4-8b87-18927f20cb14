import { Component } from '@angular/core';
import { UnicaSideNavElementItemConfig, UnicaSideNavSectionConfig } from '@hcl/unica-common';
import { appRoutes } from './angular-lib.routes';
import { Route, Router } from '@angular/router';
import { UnicaIconRegistryService } from '@hcl/angular/unica-icon';

@Component({
  selector: 'angular-lib',
  styleUrls: ['angular-lib.component.scss'],
  templateUrl: 'angular-lib.component.html',
  standalone: false
})
export class AngularLibComponent {
  /**
   * The navigation elements for this application
   */
  navSections: UnicaSideNavSectionConfig[] = [];
  /**
   * Our Side nav has a title
   */
  title: UnicaSideNavElementItemConfig = {
    id: 'header',
    icon: 'unica',
    label: 'Unica',
    order: 0
  };
  /**
   * This is the list of custom icons that we use within unica
   * @private
   */
  iconList: {key: string, url: string}[] = [
    { key: 'unica_col_module', url: 'assets/icons/deliver/col_module.svg'},
    { key: 'unica_image_module', url: 'assets/icons/deliver/image_module.svg'},
    { key: 'unica_text_module', url: 'assets/icons/deliver/text_module.svg'},
    { key: 'unica_button_module', url: 'assets/icons/deliver/button_module.svg'},
    { key: 'unica_spacer_module', url: 'assets/icons/deliver/spacer_module.svg'},
    { key: 'unica_divider_module', url: 'assets/icons/deliver/text_module.svg'},
    { key: 'unica_social_module', url: 'assets/icons/deliver/social_module.svg'},
    { key: 'unica_webpage_module', url: 'assets/icons/deliver/text_module.svg'},
    { key: 'unica_snippet_module', url: 'assets/icons/deliver/snippet_module.svg'},
    { key: 'unica_content_connector_module', url: 'assets/icons/deliver/text_module.svg'},
    { key: 'unica_hide_on_desktop', url: 'assets/icons/deliver/hide_on_desktop.svg'},
    { key: 'unica_hide_on_mobile', url: 'assets/icons/deliver/hide_on_mobile.svg'},
    { key: 'unica_align_horizontal_left', url: 'assets/icons/deliver/align_horizontal_left.svg'},
    { key: 'unica_align_horizontal_center', url: 'assets/icons/deliver/align_horizontal_center.svg'},
    { key: 'unica_align_horizontal_right', url: 'assets/icons/deliver/align_horizontal_right.svg'},
    { key: 'unica_align_vertical_top', url: 'assets/icons/deliver/align_horizontal_left.svg'},
    { key: 'unica_align_vertical_middle', url: 'assets/icons/deliver/align_horizontal_center.svg'},
    { key: 'unica_align_vertical_bottom', url: 'assets/icons/deliver/align_horizontal_right.svg'},
  ]
  /**
   *
   * Def constructor
   */
  constructor(private route: Router,
              protected iconReg: UnicaIconRegistryService) {
    this.createNavRouting();
    this.iconList.forEach((icon) => {
      this.iconReg.registerIcon(icon);
    });
  }
  /**
   * This function will use the application routing and
   * create the nav elements
   * @private
   */
  private createNavRouting(): void {
    const navRoutes = this.routesToNav(appRoutes);
    this.navSections =[
      {
        elements: [
          {
            id: 'home',
            label: 'Home',
            icon: 'home',
            path: '/home',
            order: 0
          },
          ...navRoutes
        ]
      }
    ];
  }
  /**
   * Recursively convert routes[] to nav
   * @param routes
   * @private
   */
  private routesToNav(routes: Route[], parentPath?: string): UnicaSideNavElementItemConfig[] {
    const arr: UnicaSideNavElementItemConfig[] = [];
    routes.forEach((r) => {
      // ignore default routes & home, rest will be in one
      if (r.path != '' && r.data && r.path != 'home') {
        const def: UnicaSideNavElementItemConfig = {
          id: r.data['id'],
          label: r.data['label'],
          icon: r.data['icon'],
          path: (parentPath ? parentPath + '/' : '') + r.path,
          children: [],
          order: 0
        }
        if (r.children) {
          def.children = this.routesToNav(r.children, r.path);
        }
        arr.push(def);
      }
    });
    return arr;
  }
  /**
   * Navigate
   * @param s
   */
  navigate(nav: UnicaSideNavElementItemConfig[]) {
    // get the last 1 and navigate
    const leafNode = nav[nav.length - 1];
    if (leafNode.path) {
      this.route.navigate([leafNode.path]);
    }
  }
}
