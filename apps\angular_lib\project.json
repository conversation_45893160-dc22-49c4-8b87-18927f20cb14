{"name": "angular_lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "", "sourceRoot": "apps/angular_lib/src", "tags": ["app:angular-lib"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/angular_lib", "index": "apps/angular_lib/src/index.html", "main": "apps/angular_lib/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/angular_lib/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/angular_lib/public"}, {"glob": "**/*", "input": "public/assets", "output": "assets"}], "styles": ["public/assets/styles/unica/angular/unica.scss", "apps/angular_lib/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/angular_lib/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/angular_lib/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4201, "publicHost": "http://localhost:4201"}, "configurations": {"production": {"buildTarget": "angular_lib:build:production"}, "development": {"buildTarget": "angular_lib:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular_lib:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/angular_lib/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "angular_lib:build", "port": 4201, "watch": false}, "configurations": {"development": {"buildTarget": "angular_lib:build:development"}, "production": {"buildTarget": "angular_lib:build:production"}}}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "apps/angular_lib/.storybook", "browserTarget": "angular_lib:build-storybook", "compodoc": false, "styles": ["public/assets/styles/unica/angular/unica.scss", "apps/angular_lib/src/styles.scss"]}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/angular_lib", "configDir": "apps/angular_lib/.storybook", "browserTarget": "angular_lib:build-storybook", "compodoc": false, "styles": ["public/assets/styles/unica/angular/unica.scss", "apps/angular_lib/src/styles.scss"]}, "configurations": {"ci": {"quiet": true}}}}}