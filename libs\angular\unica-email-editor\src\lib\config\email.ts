import {
  Align,
  ElementBackground, ElementBorder, ElementFont, ElementImage, ElementLineHeight, ElementLink,
  ElementPaddingMargin,
  ElementWidthHeight,
  FlowDirection, HideOnType, IDownloadDetailInfo, IRule, VerticalAlign
} from './email-common-elements';
import defaultsDeep from 'lodash-es/defaultsDeep';
import { PLACEHOLDER_IMAGE_URL } from '../helper/email-editor.constant';
/**
 * The state of the Block
 */
export interface BlockState {
  disabled: boolean;
  message: string;
}

/**
 * The text block options
 */
export interface UnicaTextBlockOptions {
  // in case a value is change we can update the element
  // using this callback
  // updateCallback?: (key, obj) => void;
  color?: string;
  font?: ElementFont;
  lineHeight?: ElementLineHeight;
  padding?: ElementPaddingMargin;
  image?: ElementImage;
  background?: ElementBackground;
  rules?: {
    name: string;
    url: string;
    linkId: string;
    startIndex: number;
    endIndex: number;
    linkText: string;
    redirection: 'lp' | 'url';
    landingPage?: string;
    ruleJson: never;
    aliasName: string;
  }[],
  hideOn?: HideOnType | undefined;
}

/**
 * The config options for the image block
 */
export interface UnicaImageBlockOptions {
  redirection?: string;
  landingPage?: string;
  url?: string;
  newWindow?: boolean;
  border?: ElementBorder;
  defaultWidth?: number;
  width?: ElementWidthHeight;
  defaultHeight?: number;
  height?: ElementWidthHeight;
  link?: ElementLink;
  align?: Align;
  title?: string;
  padding?: ElementPaddingMargin;
  image?: ElementImage;
  background?: ElementBackground;
  initialLoad?: boolean;
  rules?: IRule[];
  aliasNameInfo?: {
    name: string,
    id: string
  },
  hideOn?: HideOnType | undefined;
}

export interface UnicaButtonBlockOptions {
  redirection?: string;
  landingPage?: string;
  url?: string;
  newWindow?: boolean;
  image?: ElementImage;
  background?: ElementBackground;
  backgroundColor?: string;
  border?: ElementBorder;
  color?: string;
  font?: ElementFont;
  align?: Align;
  fullWidth?: boolean;
  lineHeight?: ElementLineHeight;
  link?: ElementLink;
  innerPadding?: ElementPaddingMargin;
  padding?: ElementPaddingMargin;
  rules?: IRule[];
  aliasNameInfo?: {
    name: string,
    id: string
  },
  hideOn?: HideOnType | undefined;
}

export interface UnicaDividerBlockOptions {
  border?: ElementBorder;
  padding?: ElementPaddingMargin;
  image?: ElementImage;
  background?: ElementBackground;
  hideOn?: HideOnType | undefined;
}

export interface UnicaSpacerBlockOptions {
  height?:ElementWidthHeight;
  width?: ElementWidthHeight;
  image?: ElementImage;
  background?: ElementBackground;
  hideOn?: HideOnType | undefined;
}

export interface UnicaWebpageBlockOptions {
  link?: ElementLink;
  title?: string;
  background?: ElementBackground;
  border?: ElementBorder;
  color?: string;
  font?: ElementFont;
  align?: Align;
  fullWidth?: boolean;
  lineHeight?: ElementLineHeight;
  padding?: ElementPaddingMargin;
  image?: ElementImage;
  aliasName?: string;
  aliasNameInfo?: {
    name: string,
    id: string
  },
  hideOn?: HideOnType | undefined;
}

export interface UnicaPreferencesUnsubscribeButtonOptions {
  redirection: 'url' | 'lp',
  landingPage?: string,
  url?: string,
  title?: string;
  background?: ElementBackground;
  border?: ElementBorder;
  color?: string;
  font?: ElementFont;
  align?: Align;
  fullWidth?: boolean;
  lineHeight?: ElementLineHeight;
  padding?: ElementPaddingMargin;
  image?: ElementImage;
  newWindow?: boolean;
  aliasNameInfo?: {
    name: string,
    id: string
  },
  hideOn?: HideOnType | undefined;
}

export type BlockType = 'text' | 'image' | 'button' | 'spacer' | 'divider' | 'social' | 'webpage' | 'snippet' | 'content-connector' | 'structure' | 'preferences-unsubscribe-link';
/**
 * The options for the options
 */
export type BlocksOptions =
  UnicaTextBlockOptions | UnicaImageBlockOptions | UnicaButtonBlockOptions | UnicaDividerBlockOptions | UnicaSpacerBlockOptions | UnicaWebpageBlockOptions | UnicaPreferencesUnsubscribeButtonOptions;
/**
 *
 */
export interface BaseBlock {
  readonly type: BlockType;
  readonly icon: string;
  readonly family: string;
  options: BlocksOptions;
  state: BlockState;
  type_display: string;
  [key: string]: any;
}
/**
 * The text block definition
 */
export class UnicaTextBlock implements BaseBlock {
  readonly type = 'text';
  readonly icon = 'text_format';
  readonly family = 'block-element';
  public label = '';
  public innerText = '';
  public type_display = '';
  public options: UnicaTextBlockOptions = {
    color: '#000000',
    font: {
      fallback: 'Arial, Helvetica, sans-serif',
      family: 'Roboto',
      style: 'normal',
      size: 16,
      weight: 400
    },
    lineHeight: {
      value: 40,
      unit: 'px'
    },
    padding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    }
  };
  public state=  {
    disabled: false,
    message: ''
  };
  constructor(options?: UnicaTextBlockOptions) {
    this.options = defaultsDeep(options, this.options);
  }
}
/**
 * The image block definition
 */
export class UnicaImageBlock implements BaseBlock {
  readonly type = 'image';
  readonly icon = 'image';
  readonly family = 'block-element';
  public label = '';
  public type_display = '';
  public src = PLACEHOLDER_IMAGE_URL;
  public downloadDetails: {
    // in case the image is a lin/
    // the Id of the asset that we want to download
    assetId?: number | string,
    // the id that needs to be given to the tag
    id?: string,
    // the URL which needs to be hit to download the asset
    assetUrl?: string,
    // flag thatr tells the current status of the asset
    assetDownloaded?: boolean
  } | IDownloadDetailInfo | null = null
  public options: UnicaImageBlockOptions = {
    redirection: 'url',
    landingPage: '',
    url: '',
    border: {
      color: '#ffffff',
      style: 'solid',
      width: 0,
      radius: 0
    },
    defaultWidth: 100,
    width: {
      value: 100,
      unit: 'px',
      auto: false,
      units: ['px']
    },
    defaultHeight: 100,
    height: {
      value: 200,
      unit: 'px',
      auto: false,
      units: ['px']
    },
    link: {
      href: '',
      target: '_blank'
    },
    align: 'center',
    title: '',
    padding: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    initialLoad: true,
  };
  // public state=  {
  //   disabled: false,
  //   message: ''
  // };
  constructor(
    label?: string,
    options?: UnicaImageBlockOptions,
    public state: BlockState = {
      disabled: false,
      message: ''
    }
  ) {
    this.options = defaultsDeep(options, this.options);
    if (label) {
      this.type_display = label;
    }
  }
}

/**
 * The button block definition
 */
export class UnicaButtonBlock implements BaseBlock {
  readonly type = 'button';
  readonly icon = 'button-module';
  readonly family = 'block-element';
  public type_display: string = '';
  public actions: IAction = new IAction();
  public errors?: Array<{ attribute: string, key: string }> = [];
  public options: UnicaButtonBlockOptions = {
    redirection: 'url',
    landingPage: '',
    url: '',
    backgroundColor: '#414141',
    border: {
      color: '#414141',
      style: 'solid',
      width: 0,
      radius: 3
    },
    color: '#ffffff',
    font: {
      fallback: 'Arial, Helvetica, sans-serif',
      family: 'Roboto',
      size: 13,
      style: 'normal',
      weight: 400
    },

    align: 'center',
    fullWidth: false,
    // verticalAlign: 'middle',
    lineHeight: {
      value: 15,
      unit: 'px'
    },
    link: {
      href: '',
      target: '_blank'
    },
    innerPadding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    },
    padding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    }
  };
  constructor(public innerText: string = "click on me",
              options?: UnicaButtonBlockOptions,
              public state: BlockState = {
                disabled: false,
                message: ''
              }
  ) {
    this.options = defaultsDeep(options, this.options);
  }
}

/**
 * The Divider block definition
 */
export class UnicaDividerBlock implements BaseBlock {
  readonly type = 'divider';
  public type_display: string = '';
  readonly icon = 'remove';
  readonly family = 'block-element';
  public actions: IAction = new IAction();
  public options: UnicaDividerBlockOptions = {
    border: {
      color: '#000000',
      style: 'solid',
      width: 4
    },
    padding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    }
  };

  constructor(
    public label?: string,
    options?: UnicaDividerBlockOptions,
    public state: BlockState = {
      disabled: false,
      message: ''
    }
  ) {
    this.options = defaultsDeep(options, this.options);
  }
}

/**
 * The Spacer block definition
 */
export class UnicaSpacerBlock implements BaseBlock {
  readonly type = 'spacer';
  public type_display: string = '';
  readonly icon = 'vertical_align_center';
  readonly family = 'block-element';
  public actions: IAction = new IAction();
  options: UnicaSpacerBlockOptions = {
    height: {
      value: 20,
      unit: 'px',
      units: ['px']
    }
  };

  constructor(
    public label?: string,
    options?: UnicaSpacerBlockOptions,
    public state: BlockState = {
      disabled: false,
      message: ''
    }
  ) {
    this.options = defaultsDeep(options, this.options);
  }
}

/**
 * The Webpage block definition
 */
export class UnicaWebpageBlock implements BaseBlock {
  readonly type = 'webpage';
  public type_display: string = '';
  readonly icon = 'open_in_browser';
  readonly family = 'block-element';
  public actions: IAction = new IAction();

  public options: UnicaWebpageBlockOptions = {
    link: {
      href: '<--SELF_LANDING_PAGE-->',
      target: '_blank'
    },
    image: {
      src: '',
      width: {
        value: 100,
        unit: 'px',
        auto: false,
        units: ['px']
      },
      height: {
        value: 30,
        unit: 'px',
        auto: false,
        units: ['px']
      },
    },
    background: {
      color: '#ffffff',
      url: '',
      repeat: 'repeat',
      size: {
        value: 100,
        unit: 'px',
        auto: true,
        units: ['px', '%', 'cover', 'contain']
      },
      width: {
        value: 100,
        unit: '%',
        auto: false,
        units: ['px', '%']
      },
      height: {
        value: 100,
        unit: '%',
        auto: false,
        units: ['px', '%']
      },
    },
    border: {
      color: '#414141',
      style: 'solid',
      width: 0,
      radius: 3
    },
    color: '#007bff',
    font: {
      fallback: 'Arial, Helvetica, sans-serif',
      family: 'Roboto',
      size: 13,
      style: 'normal',
      weight: 400
    },

    align: 'center',
    fullWidth: false,
    lineHeight: {
      value: 25,
      unit: 'px'
    },
    padding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    }
  };

  constructor(
    public innerText: string = 'Open as a webpage',
    public name: string = 'webpage',
    options?: UnicaWebpageBlockOptions,
    public state: BlockState = {
      disabled: false,
      message: ''
    }
  ) {
    this.options = defaultsDeep(options, this.options);
  }
}

/**
 * The Preferences block definition
 */
export class UnicaPreferencesUnsubscribeBlock implements BaseBlock {
  readonly type = 'preferences-unsubscribe-link';
  public type_display: string;
  readonly icon = 'open_in_browser';
  readonly family = 'block-element';
  public errors?: Array<{ attribute: string, key: string }> = [];
  public options: UnicaPreferencesUnsubscribeButtonOptions = {
    redirection: 'lp',
    landingPage: '',
    url: '',
    image: {
      src: '',
      width: {
        value: 100,
        unit: 'px',
        auto: false,
        units: ['px']
      },
      height: {
        value: 30,
        unit: 'px',
        auto: false,
        units: ['px']
      },
    },
    background: {
      color: '#ffffff',
      url: '',
      repeat: 'repeat',
      size: {
        value: 100,
        unit: 'px',
        auto: true,
        units: ['px', '%', 'cover', 'contain']
      },
      width: {
        value: 100,
        unit: '%',
        auto: false,
        units: ['px', '%']
      },
      height: {
        value: 100,
        unit: '%',
        auto: false,
        units: ['px', '%']
      },
    },
    border: {
      color: '#414141',
      style: 'solid',
      width: 0,
      radius: 3
    },
    color: '#007bff',
    font: {
      fallback: 'Arial, Helvetica, sans-serif',
      family: 'Roboto',
      size: 13,
      style: 'normal',
      weight: 400
    },

    align: 'center',
    fullWidth: false,
    // verticalAlign: 'middle',
    lineHeight: {
      value: 37,
      unit: 'px'
    },
    padding: {
      top: 10,
      right: 25,
      bottom: 10,
      left: 25
    }
  };
  public actions: IAction = new IAction(true, true, true);

  constructor(
    public isUnsubscribe: boolean = false,
    public isManagePreferences: boolean = false,
    public unsubscribeLabel: string = '',
    public managePreferencesLabel: string = '',
    public label: string = 'webpage',
    options?: UnicaPreferencesUnsubscribeButtonOptions,
    public state: BlockState = {
      disabled: false,
      message: ''
    }
  ) {
    this.options = defaultsDeep(options, this.options);
    this.type_display = label;
  }
}

/**
 * The type of blocks we support
 */
export type EmailBlock =
  UnicaTextBlock |
  UnicaImageBlock |
  UnicaButtonBlock |
  UnicaDividerBlock |
  UnicaSpacerBlock |
  UnicaWebpageBlock |
  UnicaPreferencesUnsubscribeBlock;

export type DraggableTypes = EmailBlock | UnicaStructure

export class IAction {
  public removeDelete: boolean = false;
  public removeDuplicate: boolean = false;
  public removeShuffle: boolean = false;
  constructor(removeDelete = false, removeDuplicate = false, removeShuffle = false) {
    this.removeDelete = removeDelete;
    this.removeDuplicate = removeDuplicate;
    this.removeShuffle = removeShuffle;
  }
}

/**
 * The general option of an Email based on which the
 * styling of the email behaves, this is the global settings
 */
export interface EmailGeneralOptions{
  width?: ElementWidthHeight;
  background?: ElementBackground;
  padding?: ElementPaddingMargin;
  direction?: FlowDirection;
  previewText: string;
  name?: string;
  global?: {
    fonts?: string[];
    padding?: ElementPaddingMargin;
  };
  isContentHidden?: boolean; // to hide content tab
}
/**
 * The type of layouts that we have
 */
export type StructureTypes =
  | 'cols_1'
  | 'cols_2'
  | 'cols_3'
  | 'cols_4'
  | 'cols_12'
  | 'cols_21'
  | 'custom_structure';
/**
 * This interface defines a column in 1 structure
 */
export interface UnicaStructureColumn {
  background?: ElementBackground;
  border?: ElementBorder;
  verticalAlign?: VerticalAlign;
  width?: ElementWidthHeight;
  height?: ElementWidthHeight;
}
/**
 * This defines the layout of 1 structure
 * This may have multiple columns which are defined int eh columns
 * attribute
 */
export interface UnicaStructureOptions {
  border?: ElementBorder;
  background?: ElementBackground;
  padding?: ElementPaddingMargin;
  margin?: ElementPaddingMargin;
  disableResponsive?: boolean;
  gaps?: [number, number];
  height?: ElementWidthHeight;
  columnsWidth?: number[];
  columns?: UnicaStructureColumn[];
  // actions?: IAction;
  isDefaultDesigned?: boolean; // this property will be set to true for all out f the box structures provided
  hideOn?: HideOnType | undefined;
  stacking?: string;
}
/**
 * Teh structure entity that is rendered ibn the canvas
 */
export interface UnicaStructure {
  readonly type: StructureTypes;
  // unique IDs for unique class attribute
  readonly id: number;
  options: UnicaStructureOptions;
  elements: EmailBlock[][];
  readonly columns: number;
}
/**
 * The def of the email
 */
export interface UnicaEmail {
  version?: string;
  changeLog?: object[];
  general?: EmailGeneralOptions;
  structures?: UnicaStructure[];
  forms?: never[];
}
