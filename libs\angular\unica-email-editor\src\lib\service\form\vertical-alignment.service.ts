import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';

@UntilDestroy()
@Injectable({
  providedIn: 'root', // Ensure the service is globally available
})
export class VerticalAlignmentService {
  /**
   * Subject that will be fired when the vertical alignment values are changed
   */
  private valueChangeSubject = new BehaviorSubject<string | undefined>('top'); // Default alignment
  public valueChange$ = this.valueChangeSubject.asObservable();

  /**
   * The vertical alignment form
   */
  public readonly form: FormGroup;

  constructor(private formBuilder: FormBuilder) {
    this.form = this.formBuilder.group({
      verticalAlign: ['top'], // Default alignment
    });

    // Emit value changes
    this.form.valueChanges.pipe(untilDestroyed(this)).subscribe((v) => {
      this.valueChangeSubject.next(v.verticalAlign);
    });
  }
}