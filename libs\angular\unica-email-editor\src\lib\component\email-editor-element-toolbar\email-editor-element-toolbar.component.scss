.element-toolbar-container {
  height: 100%;
  align-items: center;
  position: relative;

  // Prevent container movement during drag
  &.dragging-toolbar {
    .element-container {
      pointer-events: none;
      position: relative;

      // Prevent any visual displacement
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        z-index: 10;
      }
    }
  }

  .filler {
    height: 80px;
  }

  .toolbar-drag-handle {
    width: 46px;
    height: 20px;
    margin-left: 2px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: var(--unica-primary, #038D99);
      transform: translateY(-1px);
    }

    &:active {
      cursor: grabbing;
      background: rgba(3, 141, 153, 0.1);
    }

    .drag-handle-dots {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 2px;
      width: 12px;
      height: 8px;

      span {
        width: 2px;
        height: 2px;
        background: #666;
        border-radius: 50%;
        transition: background-color 0.2s ease;
      }
    }

    &:hover .drag-handle-dots span {
      background: var(--unica-primary, #038D99);
    }
  }

  .element-container{
    width: 46px;
    top: 96px;
    margin-left: 2px;

    .non-draggable-element {
      cursor: pointer;
    }
    .draggable-element {
      cursor: grab;
      padding: 0px 10px;
      background: #FFF;
      text-align: center;
      margin: auto;
      opacity: 0.6;
    }
  }
}
