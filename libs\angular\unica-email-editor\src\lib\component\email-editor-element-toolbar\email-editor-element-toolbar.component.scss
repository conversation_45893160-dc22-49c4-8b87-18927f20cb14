.element-toolbar-container {
  height: 100%;
  align-items: center;
  position: relative;

  // Visual feedback during drag
  &.dragging-toolbar {
    transition: all 0.2s ease;

    .element-container {
      // Keep icons functional but add subtle visual feedback
      transition: all 0.2s ease;
    }
  }

  .filler {
    height: 80px;
  }

  .toolbar-drag-handle {
    width: 46px;
    height: 24px;
    margin-left: 2px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 240, 240, 0.9));
    border: 1px solid #ddd;
    border-radius: 6px 6px 0 0;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(3, 141, 153, 0.1));
      border-color: var(--unica-primary, #038D99);
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
      background: linear-gradient(135deg, rgba(3, 141, 153, 0.2), rgba(3, 141, 153, 0.1));
      transform: translateY(0);
    }

    .drag-handle-dots {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 2px;
      width: 12px;
      height: 8px;

      span {
        width: 2px;
        height: 2px;
        background: #666;
        border-radius: 50%;
        transition: background-color 0.2s ease;
      }
    }

    &:hover .drag-handle-dots span {
      background: var(--unica-primary, #038D99);
    }
  }

  .element-container{
    width: 46px;
    top: 96px;
    margin-left: 2px;

    .non-draggable-element {
      cursor: pointer;
    }
    .draggable-element {
      cursor: grab;
      padding: 0px 10px;
      background: #FFF;
      text-align: center;
      margin: auto;
      opacity: 0.6;
    }
  }
}
