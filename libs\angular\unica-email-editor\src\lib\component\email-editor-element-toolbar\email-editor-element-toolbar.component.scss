.element-toolbar-container {
  height: 100%;
  align-items: center;
  position: relative;

  // Visual feedback during drag
  &.dragging-toolbar {
    transition: all 0.2s ease;

    .element-container {
      // Keep icons functional but add subtle visual feedback
      transition: all 0.2s ease;
    }
  }

  .filler {
    height: 80px;
  }

  .toolbar-drag-handle {
    width: 46px;
    height: 28px;
    margin-left: 2px;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.95));
    border: 1px solid #ddd;
    border-radius: 8px 8px 0 0;
    transition: all 0.15s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // Ensure this element is isolated from CDK drag system
    position: relative;
    z-index: 100;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(3, 141, 153, 0.05));
      border-color: var(--unica-primary, #038D99);
      transform: translateY(-1px);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }

    &:active,
    &:focus {
      cursor: grabbing;
      background: linear-gradient(135deg, rgba(3, 141, 153, 0.15), rgba(3, 141, 153, 0.05));
      transform: translateY(0) scale(0.98);
      outline: none;
    }

    .drag-handle-dots {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 3px;
      width: 16px;
      height: 10px;

      span {
        width: 3px;
        height: 3px;
        background: #888;
        border-radius: 50%;
        transition: all 0.15s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    &:hover .drag-handle-dots span {
      background: var(--unica-primary, #038D99);
      transform: scale(1.1);
    }

    &:active .drag-handle-dots span {
      background: var(--unica-primary, #038D99);
      transform: scale(0.9);
    }
  }

  .element-container{
    width: 46px;
    top: 96px;
    margin-left: 2px;

    .non-draggable-element {
      cursor: pointer;
    }
    .draggable-element {
      cursor: grab;
      padding: 0px 10px;
      background: #FFF;
      text-align: center;
      margin: auto;
      opacity: 0.6;
    }
  }
}
