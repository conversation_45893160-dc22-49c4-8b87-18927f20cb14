.element-toolbar-container {
  height: 100%;
  align-items: center;
  .filler {
    height: 80px;
  }
  .element-container{
    width: 46px;
    top: 96px;
    margin-left: 2px;

    &.draggable-toolbar {
      cursor: grab;
      position: relative;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        cursor: grabbing;
      }

      // Add drag indicator
      &::before {
        content: '⋮⋮';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        color: #666;
        letter-spacing: -2px;
        opacity: 0.7;
      }
    }

    .non-draggable-element {
      cursor: pointer;
    }
    .draggable-element {
      cursor: grab;
      padding: 0px 10px;
      background: #FFF;
      text-align: center;
      margin: auto;
      opacity: 0.6;
    }
  }
}
