import { Route, UrlSegment, UrlSegmentGroup } from '@angular/router';
import { AngularLibHomeComponent } from '../component/angular-lib-home/angular-lib-home.component';
import { AngularLibInputDocComponent } from '../component/angular-lib-input-doc/angular-lib-input-doc.component';
import { AngularLibTextAreaComponent } from '../component/angular-lib-text-area/angular-lib-text-area.component';
export const remoteRoutes: Route[] = [
  { path: 'home', component: AngularLibHomeComponent },
  {
    path: 'input',
    component: AngularLibInputDocComponent,
    data: {
      label: 'Input Elements',
      icon: 'edit_note',
      id: 'input'
    },
    children: [
      {
        path: 'text',
        component: AngularLibInputDocComponent,
        data: {
          label: 'Text Box',
          icon: 'text_fields',
          id: 'text'
        }
      },
      {
        path: 'text-area',
        component: AngularLibTextAreaComponent,
        data: {
          label: 'Text Area',
          icon: 'notes',
          id: 'text-area'
        }
      }
    ]
  }
]