import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { tap, distinctUntilChanged } from 'rxjs/operators';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { UnicaButtonToggleElement } from '@hcl/unica-common';
import { UnicaButtonToggleGroupComponent } from '@hcl/angular/unica-button';
import { UnicaTemplateDirective } from '@hcl/angular/unica-angular-common';
import { TranslatePipe } from '@ngx-translate/core';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { VerticalAlignmentService } from '../../../service/form/vertical-alignment.service';
import { VerticalAlign } from '../../../config/email-common-elements';

@Component({
  selector: 'element-vertical-alignment-form',
  standalone: true,
  imports: [
    CommonModule,
    UnicaIconModule,
    UnicaButtonToggleGroupComponent,
    UnicaTemplateDirective,
    TranslatePipe,
    UnicaTypographyComponent,
  ],
  providers: [VerticalAlignmentService], // Provide the service
  templateUrl: './element-vertical-alignment-form.component.html',
  styleUrls: ['./element-vertical-alignment-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ElementVerticalAlignmentFormComponent {
  /**
   * Vertical alignment state management
   */
  private _verticalAlign: VerticalAlign | undefined;
  private verticalAlignSubject = new BehaviorSubject<VerticalAlign | undefined>(undefined);
  protected verticalAlign$ = this.verticalAlignSubject.asObservable().pipe(
    tap((align) => (this._verticalAlign = align))
  );

  @Input() set verticalAlign(value: VerticalAlign | undefined) {
    this.verticalAlignSubject.next(value);
    this.form.setValue({ verticalAlign: value || 'top' });
  }

  /**
   * Vertical alignment toggle elements
   */
  protected verticalAlignmentToggleElements: UnicaButtonToggleElement[] = [
    {
      value: 'top',
      templateName: 'verticalAlignmentToggleButton',
      icon: 'unica_align_vertical_top',
      label: 'Align Top',
    },
    {
      value: 'middle',
      templateName: 'verticalAlignmentToggleButton',
      icon: 'unica_align_vertical_middle',
      label: 'Align Middle',
    },
    {
      value: 'bottom',
      templateName: 'verticalAlignmentToggleButton',
      icon: 'unica_align_vertical_bottom',
      label: 'Align Bottom',
    },
  ];

  /**
   * Constructor
   */
  constructor(
    private verticalAlignmentFormService: VerticalAlignmentService,
    private canvasService: EmailCanvasService
  ) {
    // Subscribe to form changes and update the canvas style
    this.verticalAlignmentFormService.valueChange$
      .pipe(
        distinctUntilChanged(),
        tap((v) => {
            this.canvasService.updatedFocusedElementStyle('verticalAlign', { verticalAlign: v as VerticalAlign | undefined });
        })
      )
      .subscribe();
  }

  /**
   * Get the form from the service
   */
  protected get form() {
    return this.verticalAlignmentFormService.form;
  }
}