import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input, Renderer2,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CdkConnectedOverlay, CdkOverlayOrigin, ConnectedPosition } from '@angular/cdk/overlay';
import {
  CdkDrag,
  CdkDragMove,
  CdkDragPlaceholder,
  CdkDragPreview,
  CdkDragRelease,
  CdkDropList
} from '@angular/cdk/drag-drop';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { EmailElementToolbarService } from '../../service/email-element-toolbar.service';
import { ReplaySubject } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { EmailElementToolbarConfig, EmailElementType } from '../../config/element-toolbar';
import { EmailEditorSubMenuComponent } from './email-element-sub-menu/email-editor-sub-menu.component';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { EmailElementDropList } from '../../config/email-element-drop-list';
import { EmailDropListRegistryService } from '../../service/email-drop-list-registry.service';
import { DragPreviewComponent } from '../unica-email-canvas/drag-preview/drag-preview.component';
import { EmailBlock, UnicaStructure } from '../../config/email';

/**
 * This is the toolbar from which we can
 * DND elements to the editor
 */
@UntilDestroy()
@Component({
  selector: 'email-editor-element-toolbar',
  standalone: true,
  imports: [CommonModule, CdkOverlayOrigin, CdkDropList, CdkDrag, UnicaButtonModule, UnicaIconModule, LetDirective, CdkConnectedOverlay, EmailEditorSubMenuComponent, CdkDragPlaceholder, DragPreviewComponent, CdkDragPreview],
  templateUrl: './email-editor-element-toolbar.component.html',
  styleUrl: './email-editor-element-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('expandLeft', [
      transition(':enter', [
        style({ width: '0px', overflow: 'hidden' }),
        animate('250ms ease-out', style({ width: '350px' }))
      ]),
      transition(':leave', [
        style({ width: '350px', overflow: 'hidden' }),
        animate('200ms ease-in', style({ width: '0px' }))
      ])
    ]),
    trigger('expandRight', [
      transition(':enter', [
        style({ width: '0px', overflow: 'hidden' }),
        animate('250ms ease-out', style({ width: '350px' }))
      ]),
      transition(':leave', [
        style({ width: '350px', overflow: 'hidden' }),
        animate('250ms ease-in', style({ width: '0px' }))
      ])
    ])
  ]
})
export class EmailEditorElementToolbarComponent implements AfterViewInit, EmailElementDropList {
  /**
   * The id of this drop-list
   */
  readonly id: string = 'element-toolbar';
  /**
   * The ref to CDKDropList
   */
  @ViewChild('elementToolbar') dropList!: CdkDropList;
  /**
   * Base on this flag we can position the elements toolbar
   * to the left or right, by default it is 'right'
   */
  @Input() position : 'left' | 'right' = 'right';
  /**
   * The instance to teh main Div container that holds this toolbar.
   * We have this bcoz we will have a resize observer on the height of the toolbar
   * & we will change the height of the sub-menu accordingly
   */
  @ViewChild('container') container!: ElementRef;
  /**
   * Current active panel
   */
  protected currentOpenedPanel: EmailElementType | undefined;
  /**
   * To observe that will listen the resize of the main container
   */
  private containerResizeObserver!: ResizeObserver;
  /**
   * This is the height og the toolbar that will be fired
   * on resize
   */
  protected toolbarDimensionSubject = new ReplaySubject<{h : number}>(1);
  protected toolbarDimension$ = this.toolbarDimensionSubject.asObservable();
  /**
   * The position how the overlay needs to be displayed
   */
  protected overlayPosition:ConnectedPosition[]  = [
    // this is when the toolbar is on the right
    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top'},
    // this is when the toolbar is on the left
    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top'},
  ];
  /**
   * Now it may happen that user has clicked outside the overlay
   * in such cases we will have to close it, this function will identify where the user has clicked
   * & then close the overlay
   */
  private verifyAndCloseOverlay= (event: MouseEvent) => {
    let clickedInside = false;
    let target= event.target  as HTMLElement;
    while (target != null) {
      if (target.tagName === "BODY") {
        break;
      }
      if (target.classList.contains('element-toolbar-sub-menu')) {
        clickedInside = true;
        break;
      }
      target = target.parentNode   as HTMLElement;
    }
    if (!clickedInside) {
      // event.stopPropagation();
      this.closeOverlay();
    }
  }
  /**
   * Default constructor
   * @param toolbarService : the def toolbar service
   */
  constructor(protected toolbarService: EmailElementToolbarService,
              private renderer: Renderer2,
              private cdr: ChangeDetectorRef,
              protected dropListRegistry: EmailDropListRegistryService) {
    this.toolbarService.activeSubMenu$.pipe(
      untilDestroyed(this)
    ).subscribe((m => {
      this.currentOpenedPanel = m;
    }));
  }
  /**
   * Called when user wants open the sub menu panel
   */
  openSubMenu(def: EmailElementToolbarConfig) {
    if (this.currentOpenedPanel === def.type) {
      this.toolbarService.openPanel(undefined);
    } else {
      this.toolbarService.openPanel(def.type);
    }
  }
  /**
   * We will do below things after the view is ready
   * 1. Listen to resize of the container & based on that update teh height of
   * the sub-menu
   */
  ngAfterViewInit(): void {
    this.containerResizeObserver = new ResizeObserver(this.containerResized.bind(this));
    // add the element
    this.containerResizeObserver.observe(this.container.nativeElement);
    // register this drop list
    //this.dropListRegistry.register(this.dropList);
  }
  /**
   * When ever the overlay is opened we will look for any outside click
   * so that we can close the overlay if the user clicks anywhere outside
   */
  protected onOverlayAttach(): void {
    setTimeout(() => {
      document.addEventListener('click', this.verifyAndCloseOverlay, true);
      // this is added as the toolbar of the focus will overlap this ovelay
      const subMenu = document.querySelector('.element-toolbar-sub-menu');
      if (subMenu) {
        const parent  = subMenu.parentNode?.parentNode;
        if (parent) {
          this.renderer.setStyle(parent, "z-index", "1000");
        }
      }
    });
  }
  /**
   * When the overlay is closed lets not listen to any outside clicks as this
   * will be over head
   */
  protected onOverlayDetach(): void {
    document.removeEventListener('click', this.verifyAndCloseOverlay, true);
    this.toolbarService.openPanel(undefined);
  }
  /**
   * Called when the container is resized
   */
  protected containerResized(entries: ResizeObserverEntry[]): void {
    if (entries && entries.length > 0 && entries[0].contentRect) {
      const height = entries[0].contentRect.height;
      if (this.toolbarDimensionSubject) {
        this.toolbarDimensionSubject.next({ h: height });
      }
    }
  }
  /**
   * This function will close the overlay
   */
  protected closeOverlay() {
    this.toolbarService.openPanel(undefined);
  }
  /**
   * Return the drop list
   */
  getDropList(): CdkDropList {
    return this.dropList;
  }
  /**
   * Whenever the drag starts we have to find the suitable container for it
   * so this function will tell the drop list register about the same
   * @param event
   */
  protected dragMoved(event: CdkDragMove) {
    this.dropListRegistry.dragMoved(event);
    // close the sub menu
  }
  /**
   * Called when the drag is released
   * @param event
   */
  protected dragReleased(event: CdkDragRelease) {
    this.dropListRegistry.dragReleased(event)
  }
  /**
   * Called when the element drag is started
   * @param $event
   */
  elementDragStarted(event: UnicaStructure | EmailBlock | undefined) {
    // when the drag is started we need to close the panel, so tell toolbar service that drag has started
    this.toolbarService.dragStartSubject.next(event)
  }
  /**
   * Called when the element drag is started
   * @param $event
   */
  elementDragEnded(event: UnicaStructure | EmailBlock | undefined) {
    // when the drag is started we need to close the panel, so tell toolbar service that drag has started
    this.toolbarService.dragStartSubject.next(undefined)
  }
}
