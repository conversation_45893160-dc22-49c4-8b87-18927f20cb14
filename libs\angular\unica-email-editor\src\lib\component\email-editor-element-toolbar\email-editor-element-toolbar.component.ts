import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  ConnectedPosition,
} from '@angular/cdk/overlay';
import {
  CdkDrag,
  CdkDragMove,
  CdkDragPlaceholder,
  CdkDragPreview,
  CdkDragRelease,
  CdkDropList,
} from '@angular/cdk/drag-drop';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { EmailElementToolbarService } from '../../service/email-element-toolbar.service';
import { ReplaySubject, filter } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import {
  EmailElementToolbarConfig,
  EmailElementType,
} from '../../config/element-toolbar';
import { EmailEditorSubMenuComponent } from './email-element-sub-menu/email-editor-sub-menu.component';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { EmailElementDropList } from '../../config/email-element-drop-list';
import { EmailDropListRegistryService } from '../../service/email-drop-list-registry.service';
import { DragPreviewComponent } from '../unica-email-canvas/drag-preview/drag-preview.component';
import { EmailBlock, UnicaStructure } from '../../config/email';

/**
 * This is the toolbar from which we can
 * DND elements to the editor
 */
@UntilDestroy()
@Component({
  selector: 'email-editor-element-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    CdkOverlayOrigin,
    CdkDropList,
    CdkDrag,
    UnicaButtonModule,
    UnicaIconModule,
    LetDirective,
    CdkConnectedOverlay,
    EmailEditorSubMenuComponent,
    CdkDragPlaceholder,
    DragPreviewComponent,
    CdkDragPreview,
  ],
  templateUrl: './email-editor-element-toolbar.component.html',
  styleUrl: './email-editor-element-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('expandLeft', [
      transition(':enter', [
        style({ width: '0px', overflow: 'hidden' }),
        animate('250ms ease-out', style({ width: '350px' })),
      ]),
      transition(':leave', [
        style({ width: '350px', overflow: 'hidden' }),
        animate('200ms ease-in', style({ width: '0px' })),
      ]),
    ]),
    trigger('expandRight', [
      transition(':enter', [
        style({ width: '0px', overflow: 'hidden' }),
        animate('250ms ease-out', style({ width: '350px' })),
      ]),
      transition(':leave', [
        style({ width: '350px', overflow: 'hidden' }),
        animate('250ms ease-in', style({ width: '0px' })),
      ]),
    ]),
  ],
})
export class EmailEditorElementToolbarComponent
  implements AfterViewInit, EmailElementDropList
{
  /**
   * The id of this drop-list
   */
  readonly id: string = 'element-toolbar';
  /**
   * The ref to CDKDropList
   */
  @ViewChild('elementToolbar') dropList!: CdkDropList;
  /**
   * Base on this flag we can position the elements toolbar
   * to the left or right, by default it is 'right'
   */
  @Input() position: 'left' | 'right' = 'right';

  /**
   * Flag to enable drag functionality for repositioning
   */
  @Input() isDraggable: boolean = false;

  /**
   * Event emitter for position changes
   */
  @Output() positionChange = new EventEmitter<'left' | 'right'>();

  /**
   * Event emitter for drag state changes
   */
  @Output() dragStateChange = new EventEmitter<boolean>();

  /**
   * Flag to track if the drag handle is being dragged
   */
  protected isDraggingHandle = false;

  /**
   * Mouse drag tracking
   */
  private isDragging = false;
  private startX = 0;
  private dragThreshold = 10; // pixels to move before starting drag
  private dragImage: HTMLElement | null = null;
  private currentDropZone: 'left' | 'right' | null = null;
  /**
   * The instance to teh main Div container that holds this toolbar.
   * We have this bcoz we will have a resize observer on the height of the toolbar
   * & we will change the height of the sub-menu accordingly
   */
  @ViewChild('container') container!: ElementRef;
  /**
   * Current active panel
   */
  protected currentOpenedPanel: EmailElementType | undefined;
  /**
   * To observe that will listen the resize of the main container
   */
  private containerResizeObserver!: ResizeObserver;
  /**
   * This is the height og the toolbar that will be fired
   * on resize
   */
  protected toolbarDimensionSubject = new ReplaySubject<{ h: number }>(1);
  protected toolbarDimension$ = this.toolbarDimensionSubject.asObservable();
  /**
   * The position how the overlay needs to be displayed
   */
  protected overlayPosition: ConnectedPosition[] = [
    // this is when the toolbar is on the right
    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },
    // this is when the toolbar is on the left
    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },
  ];
  /**
   * Now it may happen that user has clicked outside the overlay
   * in such cases we will have to close it, this function will identify where the user has clicked
   * & then close the overlay
   */
  private verifyAndCloseOverlay = (event: MouseEvent) => {
    let clickedInside = false;
    let target = event.target as HTMLElement;
    while (target != null) {
      if (target.tagName === 'BODY') {
        break;
      }
      if (target.classList.contains('element-toolbar-sub-menu')) {
        clickedInside = true;
        break;
      }
      target = target.parentNode as HTMLElement;
    }
    if (!clickedInside) {
      // event.stopPropagation();
      this.closeOverlay();
    }
  };
  /**
   * Default constructor
   * @param toolbarService : the def toolbar service
   */
  constructor(
    protected toolbarService: EmailElementToolbarService,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef,
    protected dropListRegistry: EmailDropListRegistryService,
  ) {
    this.toolbarService.activeSubMenu$
      .pipe(untilDestroyed(this))
      .subscribe((m) => {
        this.currentOpenedPanel = m;
      });
  }
  /**
   * Called when user wants open the sub menu panel
   */
  openSubMenu(def: EmailElementToolbarConfig) {
    if (this.currentOpenedPanel === def.type) {
      this.toolbarService.openPanel(undefined);
    } else {
      this.toolbarService.openPanel(def.type);
    }
  }
  /**
   * We will do below things after the view is ready
   * 1. Listen to resize of the container & based on that update teh height of
   * the sub-menu
   */
  ngAfterViewInit(): void {
    this.containerResizeObserver = new ResizeObserver(
      this.containerResized.bind(this),
    );
    // add the element
    this.containerResizeObserver.observe(this.container.nativeElement);
    // register this drop list
    //this.dropListRegistry.register(this.dropList);
  }
  /**
   * When ever the overlay is opened we will look for any outside click
   * so that we can close the overlay if the user clicks anywhere outside
   */
  protected onOverlayAttach(): void {
    setTimeout(() => {
      document.addEventListener('click', this.verifyAndCloseOverlay, true);
      // this is added as the toolbar of the focus will overlap this ovelay
      const subMenu = document.querySelector('.element-toolbar-sub-menu');
      if (subMenu) {
        const parent = subMenu.parentNode?.parentNode;
        if (parent) {
          this.renderer.setStyle(parent, 'z-index', '1000');
        }
      }
    });
  }
  /**
   * When the overlay is closed lets not listen to any outside clicks as this
   * will be over head
   */
  protected onOverlayDetach(): void {
    document.removeEventListener('click', this.verifyAndCloseOverlay, true);
    this.toolbarService.openPanel(undefined);
  }
  /**
   * Called when the container is resized
   */
  protected containerResized(entries: ResizeObserverEntry[]): void {
    if (entries && entries.length > 0 && entries[0].contentRect) {
      const height = entries[0].contentRect.height;
      if (this.toolbarDimensionSubject) {
        this.toolbarDimensionSubject.next({ h: height });
      }
    }
  }
  /**
   * This function will close the overlay
   */
  protected closeOverlay() {
    this.toolbarService.openPanel(undefined);
  }
  /**
   * Return the drop list
   */
  getDropList(): CdkDropList {
    return this.dropList;
  }
  /**
   * Whenever the drag starts we have to find the suitable container for it
   * so this function will tell the drop list register about the same
   * @param event
   */
  protected dragMoved(event: CdkDragMove) {
    this.dropListRegistry.dragMoved(event);
    // close the sub menu
  }
  /**
   * Called when the drag is released
   * @param event
   */
  protected dragReleased(event: CdkDragRelease) {
    this.dropListRegistry.dragReleased(event);
  }
  /**
   * Called when the element drag is started
   * @param $event
   */
  elementDragStarted(event: UnicaStructure | EmailBlock | undefined) {
    // when the drag is started we need to close the panel, so tell toolbar service that drag has started
    this.toolbarService.dragStartSubject.next(event);
  }
  /**
   * Called when the element drag is started
   * @param $event
   */
  elementDragEnded(event: UnicaStructure | EmailBlock | undefined) {
    // when the drag is started we need to close the panel, so tell toolbar service that drag has started
    this.toolbarService.dragStartSubject.next(undefined);
  }

  /**
   * Handle mouse down on drag handle - start of reliable drag system
   * @param event - The mouse event
   */
  protected onDragHandleMouseDown(event: MouseEvent): void {
    if (!this.isDraggable) return;

    console.log('Mouse down on drag handle');

    // Prevent CDK drag from interfering
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Record starting position
    this.startX = event.clientX;
    this.isDragging = false;

    // Add global mouse event listeners
    const onMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - this.startX;

      // Check if we've moved enough to start dragging
      if (!this.isDragging && Math.abs(deltaX) > this.dragThreshold) {
        this.startDrag();
      }

      if (this.isDragging) {
        // Update visual feedback and drag image position
        this.updateDragFeedback(deltaX, moveEvent.clientX, moveEvent.clientY);
      }
    };

    const onMouseUp = () => {
      if (this.isDragging) {
        this.endDrag();
      }

      // Clean up event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // Add global event listeners
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  /**
   * Start the drag operation
   */
  private startDrag(): void {
    console.log('Starting drag operation');
    this.isDragging = true;
    this.isDraggingHandle = true;

    // Create drag image
    this.createDragImage();

    // Add visual feedback to the toolbar
    const toolbarContainer = document.querySelector(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (toolbarContainer) {
      toolbarContainer.style.opacity = '0.4';
      toolbarContainer.style.transform = 'scale(0.95)';
    }

    // Notify parent that dragging started
    this.dragStateChange.emit(true);
  }

  /**
   * Create a drag image that follows the cursor
   */
  private createDragImage(): void {
    const toolbarContainer = document.querySelector(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (!toolbarContainer) return;

    // Clone the toolbar container
    this.dragImage = toolbarContainer.cloneNode(true) as HTMLElement;

    // Style the drag image
    this.dragImage.style.position = 'fixed';
    this.dragImage.style.top = '50%';
    this.dragImage.style.left = '50%';
    this.dragImage.style.transform = 'translate(-50%, -10%) scale(1.02)';
    this.dragImage.style.opacity = '0.9';
    this.dragImage.style.pointerEvents = 'none';
    this.dragImage.style.zIndex = '99999';
    this.dragImage.style.background = 'rgba(255, 255, 255, 0.95)';
    this.dragImage.style.borderRadius = '8px';
    this.dragImage.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
    this.dragImage.style.border = '2px solid var(--unica-primary, #038D99)';
    this.dragImage.style.transition = 'none';
    this.dragImage.style.height = 'auto';
    const filter = this.dragImage.querySelector('filter');
    console.log(this.dragImage.childNodes);
    if(filter) {
      filter.style.display = 'none';
    }
    // console.log(this.dragImage);

    // Remove drag handles from the clone
    const dragHandles = this.dragImage.querySelectorAll('.toolbar-drag-handle');
    dragHandles.forEach((handle) => handle.remove());

    // Add to document
    document.body.appendChild(this.dragImage);
  }

  /**
   * Update drag feedback based on movement
   * @param _deltaX - Horizontal movement distance (unused for now)
   * @param mouseX - Current mouse X position
   * @param mouseY - Current mouse Y position
   */
  private updateDragFeedback(
    _deltaX: number,
    mouseX: number,
    mouseY: number,
  ): void {
    // Update drag image position to follow cursor
    if (this.dragImage) {
      this.dragImage.style.left = `${mouseX}px`;
      this.dragImage.style.top = `${mouseY}px`;
      this.dragImage.style.transform = 'translate(-50%, -10%) scale(1.02)';
    }

    // Check which drop zone we're over
    const newDropZone = this.getDropZoneAtPosition(mouseX, mouseY);

    // Update visual feedback if drop zone changed
    if (newDropZone !== this.currentDropZone) {
      this.updateDropZoneHighlight(this.currentDropZone, newDropZone);
      this.currentDropZone = newDropZone;
    }
  }

  /**
   * Determine which drop zone is at the given position
   * @param x - Mouse X position
   * @param y - Mouse Y position
   * @returns The drop zone at this position or null
   */
  private getDropZoneAtPosition(x: number, y: number): 'left' | 'right' | null {
    const leftDropZone = document.querySelector(
      '.drop-zone-left',
    ) as HTMLElement;
    const rightDropZone = document.querySelector(
      '.drop-zone-right',
    ) as HTMLElement;

    if (leftDropZone && this.isElementVisible(leftDropZone)) {
      const rect = leftDropZone.getBoundingClientRect();
      if (
        x >= rect.left &&
        x <= rect.right &&
        y >= rect.top &&
        y <= rect.bottom
      ) {
        return 'left';
      }
    }

    if (rightDropZone && this.isElementVisible(rightDropZone)) {
      const rect = rightDropZone.getBoundingClientRect();
      if (
        x >= rect.left &&
        x <= rect.right &&
        y >= rect.top &&
        y <= rect.bottom
      ) {
        return 'right';
      }
    }

    return null;
  }

  /**
   * Check if an element is visible (has the 'visible' class)
   * @param element - The element to check
   * @returns True if the element is visible
   */
  private isElementVisible(element: HTMLElement): boolean {
    return element.classList.contains('visible');
  }

  /**
   * Update drop zone highlighting
   * @param oldZone - Previously highlighted zone
   * @param newZone - Currently highlighted zone
   */
  private updateDropZoneHighlight(
    oldZone: 'left' | 'right' | null,
    newZone: 'left' | 'right' | null,
  ): void {
    // Remove highlight from old zone
    if (oldZone) {
      const oldElement = document.querySelector(
        `.drop-zone-${oldZone}`,
      ) as HTMLElement;
      if (oldElement) {
        oldElement.classList.remove('hover');
      }
    }

    // Add highlight to new zone
    if (newZone) {
      const newElement = document.querySelector(
        `.drop-zone-${newZone}`,
      ) as HTMLElement;
      if (newElement) {
        newElement.classList.add('hover');
      }
    }
  }

  /**
   * End the drag operation and determine new position
   */
  private endDrag(): void {
    console.log('Ending drag operation, drop zone:', this.currentDropZone);
    this.isDragging = false;
    this.isDraggingHandle = false;

    // Clean up drag image
    if (this.dragImage && document.body.contains(this.dragImage)) {
      document.body.removeChild(this.dragImage);
      this.dragImage = null;
    }

    // Reset visual feedback
    const toolbarContainer = document.querySelector(
      '.element-toolbar-container',
    ) as HTMLElement;
    if (toolbarContainer) {
      toolbarContainer.style.opacity = '1';
      toolbarContainer.style.transform = 'none';
    }

    // Only change position if dropped on a valid drop zone
    if (this.currentDropZone && this.currentDropZone !== this.position) {
      console.log(
        'Valid drop detected, changing position from',
        this.position,
        'to',
        this.currentDropZone,
      );
      this.positionChange.emit(this.currentDropZone);
    } else {
      console.log('Invalid drop or same position, no change');
    }

    // Reset drop zone tracking
    this.currentDropZone = null;

    // Notify parent that dragging ended
    this.dragStateChange.emit(false);
  }
}
