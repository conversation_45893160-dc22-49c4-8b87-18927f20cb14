import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LetDirective } from '@ngrx/component';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { DomSanitizer } from '@angular/platform-browser';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { UnicaImageBlock } from '../../../config/email';
import { IRule } from '../../../config/email-common-elements';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { UnicaButtonModule } from '@hcl/angular/unica-button';

@UntilDestroy()
@Component({
  selector: 'image-dynamic-setting-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaIconModule, UnicaTypographyComponent, UnicaButtonModule, LetDirective],
  templateUrl: './image-dynamic-setting-form.component.html',
  styleUrl: './image-dynamic-setting-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ImageDynamicSettingFormComponent {
  /**
   * Get the attribute of the current element
   */
  protected elementAttribute$ = this.canvasService.attributeOfFocusedElement$;
  protected currentElement$ = this.canvasService.currentBlockElement$;
  isLoading: boolean = true;
  rules: IRule[] = [];
  block: UnicaImageBlock | undefined;
  /**
   * Default constructor
   */
  constructor(private canvasService: EmailCanvasService, 
    private dialogService: UnicaDialogService,
    private translate: TranslateService,
    private sanitizer: DomSanitizer) {
    this.elementAttribute$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (s) {
        this.rules = s.rules ? s.rules : [];
      }
    });
    this.currentElement$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (s) {
        this.block = s as UnicaImageBlock;
      }
    });
  }

  async actionClicked(event: any, index: number) {
    const ruleInfo = this.rules && this.rules?.length > 0 ? this.rules[index] : undefined;
    if (event === 'edit') {
      if (ruleInfo) {
        this.canvasService.openRuleBuilder({ 
          currentElement: this.block, 
          ruleInfo: ruleInfo
        });
      }
    } else if (event === 'delete') {
      const confirmed = await this.dialogService.confirm(
        this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
        this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.RULE_DELETION_CONFIRMATION', { 0: ruleInfo?.name }),
        this.translate.instant('MODAL.YES'),
        this.translate.instant('MODAL.NO'),
      );
      if (confirmed) {
        this.rules?.splice(index, 1);
        this.canvasService.updatedFocusedElementAttribute('rules', { rules: this.rules ? this.rules : []});
      }
    }
  }

  /**
   * Get the information of the main image block
   * and convert it into a default rule info object
   * @returns IRule type object
   */
  getDefaultRuleInfo(): IRule {
    return {
      imageSrc: this.block?.src,
      isDefault: true,
      name: this.translate.instant('EMAIL_EDITOR.LABELS.DEFAULT_CONTENT'),
      ruleJson: '',
      redirection: this.block?.options?.redirection === 'url' ? 'url' : 'lp',
      url: this.block?.options?.url,
      landingPage: this.block?.options?.landingPage
    }
  }

  /**
   * Open the rule builder dialog to add rules to the image block
   */
  addRulesToImage(): void {
    this.canvasService.openRuleBuilder({ currentElement: this.block });
  }

  /**
   * method to check if the default image source is present
   * if image spurce is present the only allow rules to be added
   * @returns 
   */
  isDefaultImageSrcPresent(): boolean {
    return this.block?.src && this.block?.src !== '' ? true : false
  }

}
