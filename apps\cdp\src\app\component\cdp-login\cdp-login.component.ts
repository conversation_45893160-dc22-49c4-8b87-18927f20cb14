import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaLoginComponent, UnicaLoginMultiStepComponent } from '@hcl/angular/unica-login';
import { FormsModule } from '@angular/forms';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { createHash, UnicaLogin } from '@hcl/unica-common';
import { TranslateService } from '@ngx-translate/core';
import { ApplicationService, UnicaSpinnerService } from '@hcl/angular/unica-angular-common';
import { CdpUserService } from '../../service/cdp-user.service';
import { Route, Router } from '@angular/router';
import { UnicaSnackBarService } from '@hcl/angular/unica-snack-bar';

@UntilDestroy()
@Component({
  selector: 'cdp-login',
  standalone: true,
  imports: [
    CommonModule,
    UnicaLoginMultiStepComponent,
    UnicaLoginComponent,
    FormsModule
  ],
  templateUrl: './cdp-login.component.html',
  styleUrl: './cdp-login.component.scss',
})
export class CdpLoginComponent {
  // the title
  protected title = '';
  // the close
  protected subTitle : string | undefined= undefined;
  // the def login details
  protected storedUserDetails: UnicaLogin | undefined;
  /**
   * @param translate
   */
  constructor(private translate: TranslateService,
              private spinnerService: UnicaSpinnerService,
              private appService: ApplicationService,
              private userService: CdpUserService,
              private router: Router,
              private snackBarService: UnicaSnackBarService ) {
    this.title = this.translate.instant('UNICA_COMMON.MESSAGES.SIGNIN');
    this.subTitle = this.translate.instant('UNICA_CDP.MESSAGES.LOGIN_TO_START');
    // listen to the user config
    this.userService.userDetailsUpdate$.pipe(untilDestroyed(this))
      .subscribe((u) => {
        this.storedUserDetails =  {userName: u?.username ?? ''};
      });
  }
  /**
   * Do the login into the system
   * @param $event
   */
  public login(loginDetails: UnicaLogin) {
    // we need to load the spinner globally
    this.spinnerService.show();
    this.userService.login(loginDetails).subscribe((user) => {
      if (user) {
        // time to do the navigation to home
        this.router.navigate(['dataplatform','home']);
        this.spinnerService.hide();
      }
    });
  }
  /**
   * @param $event
   */
  protected moveToNextScreen(event: {login: UnicaLogin, to: 'login' | 'password'}) {
    if(event.to === 'login'){
      this.title = this.translate.instant('UNICA_COMMON.MESSAGES.SIGNIN');
      this.subTitle = this.translate.instant('UNICA_CDP.MESSAGES.LOGIN_TO_START');
    } else {
      this.title = this.translate.instant('UNICA_COMMON.MESSAGES.HELLO_USER', {
        user: event.login.userName
      });
      this.subTitle = undefined;
    }
  }
}
