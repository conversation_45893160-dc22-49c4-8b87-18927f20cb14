<ng-container
  *ngrxLet="isDisabled$; let isDisabled">
  <mat-form-field appearance="outline"
                  class="unica-input"
                  *ngrxLet="form$; let form"
                  style="width: 100%"
                  [ngClass]="[variant === 'full' ? 'full-width' : '', customClass || '']">
    <mat-label *ngIf="label">{{ label }}</mat-label>

    <input
      matInput
      [formControl]="form"
      [disabled]="isDisabled"
      [type]="type"
      [placeholder]="placeholder"
      (input)="inputChangeEvent($event)"
      #input>
    @if (prefixIcon) {
      <button matPrefix mat-icon-button
              [disabled]="isDisabled"
              (click)="prefixIconClick.emit()" type="button">
        <unica-icon [name]="prefixIcon"></unica-icon>
      </button>
    }
    @if (form.value && clearable) {
      <button matSuffix mat-icon-button aria-label="Clear"
              [disabled]="isDisabled"
              (click)="fc.setValue('')" type="button">
        <unica-icon [name]="'close'"></unica-icon>
      </button>
    } @else if (isPasswordField) {
      <button matSuffix mat-icon-button type="button"
              (click)="toggleType()">
        <unica-icon [name]="'visibility'"></unica-icon>
      </button>
    } @else if (postfixIcon) {
      <button matSuffix mat-icon-button
              [disabled]="isDisabled"
              type="button" (click)="postfixIconClick.emit()">
        <unica-icon [name]="postfixIcon"></unica-icon>
      </button>
    }
    @if (hint) {
      <mat-hint>{{ hint }}</mat-hint>
    }
    @if (form && form.errors) {
      <mat-error>
        {{ getError(fc.errors) }}
      </mat-error>
    }
  </mat-form-field>

</ng-container>
