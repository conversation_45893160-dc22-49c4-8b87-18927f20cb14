.unica-dialog-panel {
  .mat-mdc-dialog-container {
    box-shadow:
      0 11px 15px -7px #0003,
      0 24px 38px 3px #00000024,
      0 9px 46px 8px #0000001f;
    color: #000000de;
    border-radius: var(--unica-radius-max, 8px);
    background-color: var(--unica-bg-overlay-pane, #ffffff);
    display: block;
    padding: 24px;
    box-sizing: border-box;
    overflow: auto;
    outline: 0;
    width: 100%;
    height: 100%;
    min-height: inherit;
    max-height: inherit;

    .mdc-dialog__surface {
      background-color: transparent;
      border-radius: 0;
    }
    [mat-dialog-title].mat-mdc-dialog-title {
      margin: 0 0 20px;
      padding: 0;
    }
    .mat-mdc-dialog-title::before {
      display: none;
    }
    h1.mat-mdc-dialog-title {
      color: var(--unica-text-primary, #1d1d23);
      font-size: 16px;
      margin: 0 0 20px;
      display: block;
    }
    .mat-mdc-dialog-title + .mat-mdc-dialog-content {
      padding: 0;
      overflow: hidden;
    }
    .mat-mdc-dialog-actions {
      padding: 10px 0 0;
      overflow: hidden;
      &.mat-mdc-dialog-actions-align-end,
      &[align='end'] {
        justify-content: flex-end;
      }
    }
  }

  // Resizable dialog styling
  .dialog-container {
    width: unset !important;
    height: unset !important;
    max-width: 100% !important;

    .mat-mdc-dialog-container {
      max-width: 900px !important;
      max-height: 600px !important;
      height: 100%;
      width: 100%;
      resize: both;
      min-width: 300px;
      min-height: 200px;
    }
  }

  // Drag handle styling
  .dragHold {
    cursor: grab;
  }

  // Button spacing
  .confirm-button {
    margin-right: 10px;
  }
}
