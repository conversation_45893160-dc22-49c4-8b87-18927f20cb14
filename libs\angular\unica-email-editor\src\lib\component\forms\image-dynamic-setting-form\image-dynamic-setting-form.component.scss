.image-dynamic-setting-form {
    .rule-info-container {
        letter-spacing: normal;
        text-align: left;
        color: #6D7692;
        float: left;
        .rule-name {
            font-size: 14px;
            line-height: 18px;
            letter-spacing: normal;
            font-family: Montserrat;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            padding: 20px 5px 20px 15px;
            font-weight: 600;
        }
        .loader-content {
            display: table-cell;
            vertical-align: middle;
            color: #FFFFFF;
            position: relative;
            font-size: 10px;
            padding-top: 25%;
            padding-left: 7px;
            width: 80px;
        }
        img {
            width: 83px;
            height: 55px;
        }
        .loaderdiv {
            width: 83px; 
            height: 55px;
            background: black;
            opacity: 0.6;
            position: absolute;
        }
        .imgalignment{
            padding-left: 13px;
        }
    }
    .actions-container {
        padding: 15px 0px;
        color: #6d7692;
        float: left;
        width: 10%;
        display: inline-flex;
        .edit-delete-icon {
            height: 30px;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;
    
        }
        .mr-10 {
            margin-right: 10px;
        }
    }
    .add-dynamic-button-container {
        text-align: center;
    }
}