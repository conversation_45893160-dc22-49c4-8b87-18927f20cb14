import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, ValidationErrors  } from '@angular/forms';
import { AddLinkFormDataConfig } from '../../../config/email-common-elements'

@Component({
  selector: 'add-link-popover-form',
  standalone: true,
  imports: [CommonModule, MatFormFieldModule, MatInputModule, MatButtonModule, ReactiveFormsModule, MatIconModule, MatRadioModule],
  templateUrl: './add-link-popover-form.component.html',
  styleUrl: './add-link-popover-form.component.scss',
})
export class AddLinkPopoverFormComponent implements OnInit {
  @Input() initialData!: AddLinkFormDataConfig;
  @Input() blockName: string = '';
  @Output() closePopover = new EventEmitter<void>();
  @Output() addLinkFormData = new EventEmitter<AddLinkFormDataConfig>();
  linkPopoverForm!: FormGroup;

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.initForm();
    if (this.initialData) {
      this.linkPopoverForm.patchValue({
        linkType: this.initialData.redirection,
        url: this.initialData.redirection === 'url' ? this.initialData.url : '',
        landingPage: this.initialData.landingPage,
      });
      this.setLinkType(this.initialData.redirection);
    }
  }

  /**
   * Initializes the link popover form with default values and validators.
   */
  private initForm(): void {
    this.linkPopoverForm = this.formBuilder.group({
      linkType: ['url'], // default to URL
      url: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]],
      landingPage: [{ value: '', disabled: true }]
    });
  }

  /**
   * Updates the selected link type in the form and toggles the 'url' control accordingly.
   */
  setLinkType(type: 'url' | 'lp') {
    this.linkPopoverForm.get('linkType')?.setValue(type);
    this.linkPopoverForm.get('url')?.[type === 'url' ? 'enable' : 'disable']();
  }

  /**
   * Handles the cancel action by emitting the `closePopover` event.
   * This method is triggered when the user cancels the operation,
   * closing the popover without saving the link.
   */
  onCancel(): void {
    this.closePopover.emit();
  }

  /**
   * Handles the save action by emitting the `linkSaved` event with the current `linkUrl`.
   * This method is triggered when the user saves the link, passing the link URL to the parent component.
   */
  onSave() {
    if (this.linkPopoverForm.valid) {
      const selectedLinkType = this.linkPopoverForm.get('linkType')?.value;
      const formData = {
        redirection: selectedLinkType, // 'url' or 'lp'
        url: selectedLinkType === 'url' ? this.linkPopoverForm.get('url')?.value : 'lp://3596', //TODO: Update proper Lp URL when integrating with commEditor
        landingPage: selectedLinkType === 'lp' ? this.linkPopoverForm.get('landingPage')?.value : '',
        aliasNameInfo: {
          name: this.linkPopoverForm.get('aliasName')?.value || '',
          id: 'aFGTXldNfW' //TODO: Update proper id when integrating with commEditor
        },
        newWindow: this.linkPopoverForm.get('newWindow')?.value || false
      };
      this.addLinkFormData.emit(formData);
    }
  }

  /**
   * Returns a validation error message based on the provided error object.
   */
  getErrorMessage(errors: ValidationErrors | null | undefined): string {
    if (!errors) return '';

    if (errors['required']) {
      return 'URL is required.';
    }

    if (errors['pattern']) {
      return 'URL must start with http:// or https://';
    }

    return 'Invalid input.';
  }

  /**
   * Handles the landing page browse button click.
   */
  onBrowseLP(event: MouseEvent): void {
    // Simulate selection of a landing page
    event.preventDefault(); // required to prevent default behavior from closing the popover
    this.linkPopoverForm.get('landingPage')?.setValue('02 June Lp'); //TODO: update with proper LP selection
  }
}
