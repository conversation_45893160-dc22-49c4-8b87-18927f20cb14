{"compileOnSave": false, "compilerOptions": {"useDefineForClassFields": false, "rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@hcl/angular/cdp-common": ["libs/angular/cdp/cdp-common/src/index.ts"], "@hcl/angular/unica-accordion": ["libs/angular/unica-accordion/src/index.ts"], "@hcl/angular/unica-angular-common": ["libs/angular/unica-angular-common/src/index.ts"], "@hcl/angular/unica-app-bar": ["libs/angular/unica-app-bar/src/index.ts"], "@hcl/angular/unica-button": ["libs/angular/unica-button/src/index.ts"], "@hcl/angular/unica-dashboard-builder": ["libs/angular/unica-dashboard-builder/src/index.ts"], "@hcl/angular/unica-date-picker": ["libs/angular/unica-date-picker/src/index.ts"], "@hcl/angular/unica-dialog": ["libs/angular/unica-dialog/src/index.ts"], "@hcl/angular/unica-dropdown": ["libs/angular/unica-dropdown/src/index.ts"], "@hcl/angular/unica-email-editor": ["libs/angular/unica-email-editor/src/index.ts"], "@hcl/angular/unica-icon": ["libs/angular/unica-icon/src/index.ts"], "@hcl/angular/unica-input": ["libs/angular/unica-input/src/index.ts"], "@hcl/angular/unica-link-popover": ["libs/angular/unica-link-popover/src/index.ts"], "@hcl/angular/unica-login": ["libs/angular/unica-login/src/index.ts"], "@hcl/angular/unica-menu": ["libs/angular/unica-menu/src/index.ts"], "@hcl/angular/unica-panel": ["libs/angular/unica-panel/src/index.ts"], "@hcl/angular/unica-side-nav": ["libs/angular/unica-side-nav/src/index.ts"], "@hcl/angular/unica-slide-toggle": ["libs/angular/unica-slide-toggle/src/index.ts"], "@hcl/angular/unica-snack-bar": ["libs/angular/unica-snack-bar/src/index.ts"], "@hcl/angular/unica-textarea": ["libs/angular/unica-textarea/src/index.ts"], "@hcl/angular/unica-typography": ["libs/angular/unica-typography/src/index.ts"], "@hcl/unica-common": ["libs/common/unica-common/index.ts"], "@hcl/angular/unica-text-editor": ["libs/angular/unica-text-editor/src/index.ts"], "angular_lib/Module": ["apps/angular_lib/src/app/remote-entry/remote-entry.module.ts"], "cdp/Routes": ["apps/cdp/src/app/remote-entry/entry.routes.ts"], "detect_ui/Module": ["apps/detect_ui/src/app/remote-entry/entry.module.ts"], "react_lib/Module": ["apps/react_lib/src/remote-entry.ts"]}}, "exclude": ["node_modules", "tmp"]}