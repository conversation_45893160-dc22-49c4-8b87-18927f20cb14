<focus-overlay
  #focusOverLay
  *ngrxLet="style$; let style"
  (click)="focus($event)"
  [items]="imageToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false">
  <ng-container *ngIf="isEmpty$ | async">
    <div
      class="empty-image-place-holder"
      tabindex="0"
      style="text-align: center; cursor: pointer; min-height: 200px"
      *ngrxLet="emptyImageStyle$; let emptyStyle"
      [elementHeight]="emptyStyle.height ?? null"
      [elementLineHeight]="emptyStyle.lineHeight ?? null"
      [elementWidth]="emptyStyle.width ?? null">

      <unica-button variant="outlined" [width]="'150px'" (clickAction)="openImagePanel()">
        Change Image
      </unica-button>
    </div>
  </ng-container>
  <div class="full-width" *ngIf="!(isEmpty$ | async)">
    <ng-container *ngrxLet="block$; let block">
      <div
        class="image-place-holder"
        tabindex="0"
        [elementAlign]="(alignSubject$ | async) ?? null">
        <img [src]="(imageSourceSubject$ | async) ?? null"
          [elementPadding]="(paddingSubject$ | async) ?? null"
          [elementBorder]="(borderSubject$ | async) ?? null"
          [elementHeight]="(heightSubject$ | async) ?? null"
          [elementWidth]="(widthSubject$ | async) ?? null"
        >
      </div>
    </ng-container>
  </div>
</focus-overlay>
