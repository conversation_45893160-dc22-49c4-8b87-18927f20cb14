<div
  class="element-toolbar-container"
  cdkOverlayOrigin
  #container
  #elementTarget="cdkOverlayOrigin">
  <div class="filler">
    &nbsp;&nbsp;
  </div>
  <!--  This is the element that contains all the elements -->
  <div
    *ngrxLet="toolbarService.toolbarItems$; let items"
    class="element-container"
    [class.draggable-toolbar]="isDraggable"
    cdkDropList
    [cdkDropListSortingDisabled]="true"
    [cdkDropListData]="items"
    [cdkDropListConnectedTo]="dropListRegistry.dropLists"
    #elementToolbar="cdkDropList"
    id="{{id}}"
    [draggable]="isDraggable"
    (dragstart)="onToolbarDragStart($event)"
    (dragend)="onToolbarDragEnd($event)">

    <ng-container
      *ngFor="let def of items">

      <unica-button
        *ngIf="!def.draggable"
        [title]="def.label ?? ''"
        style="padding: 0px; "
        [type]="'button'"
        [variant]="'icon'"
        (clickAction)="openSubMenu(def)"
        tabindex="0"
        class="nav-item">
        <unica-icon [name]="def.icon ?? 'unica_'"></unica-icon>
      </unica-button>

      <div
        cdkDrag
        (cdkDragMoved)="dragMoved($event)"
        (cdkDragReleased)="dragReleased($event)"
        (cdkDragStarted)="elementDragStarted(def.config)"
        (cdkDragEnded)="elementDragEnded(def.config)"
        [cdkDragData]="def.config"
        [title]="def.label ?? ''"
        style="padding: 0px 10px; "
        class="draggable-element"
        *ngIf="def.draggable">
        <unica-icon [name]="def.icon ?? 'unica_'"></unica-icon>

        <div
          [title]="def.label ?? ''"
          class="draggable-element"
          *cdkDragPlaceholder>
          <unica-icon [name]="def.icon ?? 'unica_'"></unica-icon>
        </div>

        <drag-preview *cdkDragPreview>
          <unica-icon [name]="def.icon ?? 'unica_'" style="margin: auto"></unica-icon>
        </drag-preview>

      </div>

    </ng-container>

  </div>

  <!--This is the template of the sub menu-->
  <ng-template
    *ngrxLet="toolbarService.activeSubMenu$; let activeSubMenu"
    cdkConnectedOverlay
    [cdkConnectedOverlayPush]="true"
    [cdkConnectedOverlayOpen]="activeSubMenu !== undefined"
    [cdkConnectedOverlayOrigin]="elementTarget"
    [cdkConnectedOverlayPositions]="overlayPosition"
    (attach)="onOverlayAttach()"
    (detach)="onOverlayDetach()"
  >
    <ng-container
      *ngrxLet="toolbarDimension$; let dim">
      <div
        class="element-toolbar-sub-menu"
        style="margin-left:51px;position: relative"
        *ngIf="position === 'right'"
        [@expandLeft]
        [ngStyle]="{'height': (toolbarDimension$ | async)?.h + 'px',
                    'display': (this.toolbarService.dragStart$ | async) ? 'none' : 'block' }">
          <email-editor-sub-menu
            [type]="activeSubMenu"></email-editor-sub-menu>
      </div>

      <div
        class="element-toolbar-sub-menu"
        style="margin-left:51px;position: absolute"
        *ngIf="position === 'left'"
        [@expandRight]
        [ngStyle]="{'height': (toolbarDimension$ | async)?.h + 'px',
                    'display': (this.toolbarService.dragStart$ | async) ? 'none' : 'block' }">
          <email-editor-sub-menu
            [type]="activeSubMenu"></email-editor-sub-menu>
      </div>

    </ng-container>

  </ng-template>

</div>
