import { AfterViewInit, Component, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaDropdownComponent } from '@hcl/angular/unica-dropdown';
import { UnicaCardComponent } from '@hcl/angular/unica-panel';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UnicaInputComponent } from '@hcl/angular/unica-input';
import { ValidationError } from '@nx/angular/src/generators/ng-add/utilities';
import { FormControl } from '@angular/forms';
import { UnicaOptions } from '@hcl/unica-common';

@Component({
  selector: 'angular-lib-select-box',
  standalone: true,
  imports: [CommonModule, UnicaDropdownComponent, UnicaCardComponent, UnicaTypographyComponent],
  templateUrl: './angular-lib-select-box.component.html',
  styleUrl: './angular-lib-select-box.component.scss',
})
export class AngularLibSelectBoxComponent implements AfterViewInit{

  @ViewChild('stringDropDown') stringDropDown: UnicaInputComponent | undefined

  fc: FormControl = new FormControl(['opt4']);

  protected optionsString: string[] = [
    'str-Opt 1',
    'str-Apt 2',
    'str-Bpt 3',
    'str-Zpt 4',
  ]

  protected optionsUnica: { label: string, value: string}[] = [
    { label: 'Opt 1', value: 'opt1'},
    { label: 'Apt 2', value: 'opt2'},
    { label: 'Bpt 3', value: 'opt3'},
    { label: 'Zpt 4', value: 'opt4'}
  ]



  protected optionsObject: { myLabel: string, myValue: string}[] = [
    { myLabel: 'my-Opt 1', myValue: 'opt1'},
    { myLabel: 'my-Apt 2', myValue: 'opt2'},
    { myLabel: 'my-Bpt 3', myValue: 'opt3'},
    { myLabel: 'my-Zpt 4', myValue: 'opt4'}
  ];

  protected optionsCustomTemplate: { myLabel: string, myValue: string}[] = [
    { myLabel: 'my-Opt 1', myValue: 'opt1'},
    { myLabel: 'my-Apt 2', myValue: 'opt2'},
    { myLabel: 'my-Bpt 3', myValue: 'opt3'},
    { myLabel: 'my-Zpt 4', myValue: 'opt4'}
  ]

  
  // getOptionValue(obj: { myLabel: string, myValue: string}) : string {
  //   return obj.myValue;
  // }
  // getOptionLabel(obj: { myLabel: string, myValue: string}) : string {
  //   return obj.myLabel;
  // }

  getOptionValue(option: string | object | UnicaOptions) : string {
    if (typeof option === 'string') {
      // Handle string case
      return `String: ${option}`;
    }
    if (typeof option === 'object' && option !== null) {
      // Narrow down to UnicaOptions if it's the correct shape
      if ((option as UnicaOptions)['myValue']) {
        const unicaOption = option as UnicaOptions;
        return `${unicaOption['myValue']}`;
      }
      // Handle the generic object case
      return 'Generic object';
    }
    // If it's not a string or object, you can handle other cases here (or throw an error if needed)
    return 'Unknown';
  }

  getOptionLabel(option: string | object | UnicaOptions) : string {
    if (typeof option === 'string') {
      // Handle string case
      return `String: ${option}`;
    }
    if (typeof option === 'object' && option !== null) {
      // Narrow down to UnicaOptions if it's the correct shape
      if ((option as UnicaOptions)['myLabel']) {
        const unicaOption = option as UnicaOptions;
        return `${unicaOption['myLabel']}`;
      }
      // Handle the generic object case
      return 'Generic object';
    }
    // If it's not a string or object, you can handle other cases here (or throw an error if needed)
    return 'Unknown';
  }

  getErrors( field: string, err: ValidationError) : string {
    return 'This value is required';
  }

  ngAfterViewInit(): void {
    if (this.stringDropDown) {
      this.stringDropDown.focus();
    }
  }
  

}
