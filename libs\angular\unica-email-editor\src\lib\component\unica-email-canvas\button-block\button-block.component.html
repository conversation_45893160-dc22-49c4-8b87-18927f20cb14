<focus-overlay
  #focusOverLay
  (click)="focus($event)"
  *ngrxLet="style$; let style"
  [items]="buttonToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false" style="position: relative;"
  [nodeAlign]="(alignSubject$ | async) ?? null">
  <div *ngrxLet="block$; let block"  class="button-block-container"
    [elementAlign]="block.options.align ?? null" [elementFullWidth]="(fullWidthSubject$ | async) ?? null">
    <button 
      type="button"
      [elementBackground]="(backgroundSubject$ | async) ?? null"
      [elementLineHeight]="(lineHeightSubject$ | async) ?? null"
      [elementPadding]="(innerPaddingSubject$ | async) ?? null"
      [elementBorder]="(borderSubject$ | async) ?? null"
      [elementColor]="(colorSubject$ | async) ?? null"
      [elementFont]="(fontSubject$ | async) ?? null"
      [elementMargin]="(paddingSubject$ | async) ?? null"
    >
     {{innerTextSubject$ | async}}
    </button>
  </div>
</focus-overlay>