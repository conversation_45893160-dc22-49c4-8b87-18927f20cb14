<div class="form-group button-dynamic-setting-form">
    <div *ngrxLet="elementAttribute$; let attribute">
        <ng-container *ngIf="attribute?.rules?.length || 0">
            <ng-container *ngTemplateOutlet="ruleTemplate; context: { $implicit: getDefaultRuleInfo() }">
            </ng-container>
            <unica-typography [variant]="'text'">
                {{ 'EMAIL_EDITOR.LABELS.DYNAMIC_CONTENT' | translate }} ({{ attribute?.rules?.length || 0 }})
            </unica-typography>
            @for (ruleInfo of attribute?.rules; track ruleInfo; let i = $index) {
                <!-- Use ngTemplateOutlet to render the rule template -->
                <ng-container *ngTemplateOutlet="ruleTemplate; context: { $implicit: ruleInfo, index: i }">
                </ng-container>
            }
        </ng-container>
        <ng-container>
            <div class="add-dynamic-button-container">
                <unica-button
                    (clickAction)="addRulesToButton()"
                    [type]="'button'"
                    [width]="'180px'">
                    {{ 'EMAIL_EDITOR.LABELS.ADD_DYNAMIC_CONTENT' | translate }}
                </unica-button>
            </div>
        </ng-container>
    </div>
    <!-- Define the reusable template for button rule-->
    <ng-template #ruleTemplate let-ruleInfo let-i="index">
        <div class="rule-info-container" [ngClass]="ruleInfo.isDefault ? 'full-width' : 'w-80'">
        <div class="rule-name pt-0 pb-0">
            {{ ruleInfo.name }}
            <div class="hyperlink-url-container">
            <div class="url-btn-info w-90 float-left">
                <div class="overflow-hidden w-100">
                <div class="w-30 float-left" *ngIf="ruleInfo.buttonText">
                    <div class="ellipsis">{{ ruleInfo?.buttonText }}</div>
                </div>
                <div
                    class="ellipsis float-left"
                    [ngClass]="{ 'w-70': ruleInfo.buttonText }"
                    *ngIf="ruleInfo.url">
                    {{ ruleInfo.buttonText ? '|' : '' }}
                    {{ ruleInfo.redirection === 'url' ? 'URL: ' : 'LP: ' }}
                    <span class="url-link">
                    {{ ruleInfo.redirection === 'url' ? ruleInfo.url : ruleInfo.landingPage }}
                    </span>
                </div>
                </div>
            </div>
            <div
                class="w-10 float-left mt-minus-2"
                *ngIf="ruleInfo.url && ruleInfo.redirection === 'url'">
                <unica-icon
                [name]="'sort'"
                (click)="openLinkInNewTab(ruleInfo.url)">
                </unica-icon>
            </div>
            </div>
        </div>
        </div>
        <div class="actions-container pt-2 pb-2" *ngIf="!ruleInfo.isDefault">
        <div class="edit-delete-icon">
            <unica-icon
            [name]="'edit'"
            (click)="actionClicked('edit', i);"
            class="mr-10">
            </unica-icon>
        </div>
        <div class="edit-delete-icon">
            <unica-icon
            [name]="'delete'"
            (click)="actionClicked('delete', i);">
            </unica-icon>
        </div>
        </div>
    </ng-template>
</div>
