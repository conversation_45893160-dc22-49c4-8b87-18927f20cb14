import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { UnicaSpinnerComponent, UnicaSpinnerService, UnicaThemeComponent } from '@hcl/angular/unica-angular-common';
import { CdpAppBarComponent } from '../cdp-app-bar/cdp-app-bar.component';
import { CdpSideNavComponent } from '../cdp-side-nav/cdp-side-nav.component';
import { CdpUserService } from '../../service/cdp-user.service';
import { LetDirective } from '@ngrx/component';

/**
 * Teh main component when cdp is rendered as stand-alone application
 */
@Component({
  selector: 'cdp-home',
  standalone: true,
  imports: [CommonModule, RouterOutlet, UnicaSpinnerComponent, UnicaThemeComponent, CdpAppBarComponent, CdpSideNavComponent, LetDirective],
  templateUrl: './cdp-ui.component.html',
  styleUrl: './cdp-ui.component.scss',
})
export class CdpUiComponent {
  /**
   *
   * @param spinnerService
   */
  constructor(protected spinnerService: UnicaSpinnerService,
              protected userService: CdpUserService) {
  }
}
