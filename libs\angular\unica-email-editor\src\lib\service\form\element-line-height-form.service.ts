import { Injectable } from '@angular/core';
import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';
import { ElementLineHeight, ElementLineHeightUnit } from '../../config/email-common-elements';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@UntilDestroy()
@Injectable()
export class ElementLineHeightFormService {
  /**
   * Subject that will be fired when the border values are changed
   */
  private valueChangeSubject = new BehaviorSubject<ElementLineHeight | undefined>(undefined);
  public valueChange$ = this.valueChangeSubject.asObservable()
  /**
   * the login form
   * @private
   */
  public readonly form: FormGroup;
  /**
   * The validation that we need to add to the form
   * @private
   */
  public validators: {[key:string]: Validators[]} = {
    value: ['', [Validators.min(0)]],
    unit: ['', []]
  };

  get lineHeightUnitOptionsList(): ElementLineHeightUnit[] {
    return ['px', 'none'];
  }
  /**
   * @param formBuilder
   */
  constructor(private formBuilder: FormBuilder) {
    this.form = this.formBuilder.group(this.validators);
    this.form.valueChanges.pipe(untilDestroyed(this)).subscribe((v) => {
      this.valueChangeSubject.next(v);
    })
  }
}
