<focus-overlay
  #focusOverLay (click)="focus($event)"
  *ngrxLet="style$; let style"
  [items]="spacerToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false" style="position: relative;">
    <div *ngrxLet="block$; let block" style="display: table;width: 100%;" [elementHeight]="(heightSubject$ | async) ?? null"></div>
</focus-overlay>
