import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FocusableEmailElement } from '../../../config/email-element-drop-list';
import { GenerateUniqueId } from '@hcl/unica-common';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailDefaultService } from '../../../service/email-default.service';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { EmailElementType } from '../../../config/element-toolbar';
import {
  Align,
  ElementAttribute,
  ElementBackground,
  ElementBorder,
  ElementFont,
  ElementLineHeight,
  ElementPaddingMargin,
  ElementStyle,
  HideOnType,
  IRule,
  StyleAttribute,
  AddLinkFormDataConfig
} from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { ElementWidthDirective } from '../../../directive/element-width.directive';
import { ElementLineHeightDirective } from '../../../directive/element-line-height.directive';
import { ElementBackgroundDirective } from '../../../directive/element-background.directive';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { ElementPaddingDirective } from '../../../directive/element-padding.directive';
import { ElementBorderDirective } from '../../../directive/element-border.directive';
import { ElementColorDirective } from '../../../directive/element-color.directive';
import { ElementFontDirective } from '../../../directive/element-font.directive';
import { ElementAlignDirective } from '../../../directive/element-align.directive';
import { ElementMarginDirective } from '../../../directive/element-margin.directive';
import { ElementFullWidthDirective } from '../../../directive/element-full-width.directive';
import { EmailBlock, UnicaButtonBlock } from '../../../config/email';
import { UnicaPopoverService } from '@hcl/angular/unica-dialog';
import { BlockActionsService } from '../../../service/block-actions.service';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AddLinkPopoverFormComponent } from '../../forms/add-link-popover-form/add-link-popover-form.component';
import { TranslateService } from '@ngx-translate/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'button-block',
  standalone: true,
  providers: [],
  imports: [CommonModule, LetDirective, ElementWidthDirective, ElementLineHeightDirective, ElementMarginDirective, ElementBackgroundDirective, ElementPaddingDirective, ElementBorderDirective, ElementColorDirective, ElementFontDirective, ElementAlignDirective, ElementFullWidthDirective, FocusOverlayComponent],
  templateUrl: './button-block.component.html',
  styleUrl: './button-block.component.scss',
})
export class ButtonBlockComponent implements FocusableEmailElement {
  /**
   * The unique id
   */
  private _id: string = GenerateUniqueId(13);
  private _dropListId = '';
  private _blockIndex = 0;
  private addLinkFormData!: AddLinkFormDataConfig;

  get id(): string {
    return this._dropListId
      ? `${this._dropListId}_${this._blockIndex}`
      : this._id;
  }

  @Input() set dropListId(value: string) {
    this._dropListId = value;
  }

  @Input() set blockIndex(value: number) {
    this._blockIndex = value;
  }
  /**
   * The input for the block
   */
  private _block = new UnicaButtonBlock();
  private blockSubject = new BehaviorSubject<UnicaButtonBlock>(this._block);
  protected block$ = this.blockSubject
    .asObservable()
    .pipe(tap((b) => (this._block = b)));
  @Input({
    transform: (value: EmailBlock): UnicaButtonBlock => <UnicaButtonBlock>value,
  })
  set block(b: UnicaButtonBlock) {
    this.blockSubject.next(b);
    this.backgroundSubject.next({ color: b.options.backgroundColor });
    this.borderSubject.next(b.options.border);
    this.paddingSubject.next(b.options.padding);
    this.innerPaddingSubject.next(b.options.innerPadding);
    this.lineHeightSubject.next(b.options.lineHeight);
    this.colorSubject.next(b.options.color);
    this.fontSubject.next(b.options.font);
    this.innerTextSubject.next(b.innerText);
    this.fullWidthSubject.next(b.options.fullWidth);
    this.hideOnSubject.next(b.options.hideOn);
    this.alignSubject.next(b.options.align);
    this.rulesSubject.next(b.options.rules);
  }
  /**
   * The style
   * @protected
   */
  protected style$ = this.block$.pipe(
    map((b) => {
      if (b) {
        return b.options;
      }
      return undefined;
    }),
  );

  /**
   * For canvas, Whe it is empty we have below actions
   */
  protected buttonToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings',
    },
    {
      id: 'duplicate',
      icon: 'content_copy',
      label: 'Duplicate',
    },
    {
      id: 'link',
      icon: 'link',
      label: 'link',
    },
    {
      id: 'dynamic',
      icon: 'close',
      label: 'add dynamic content',
    },
    {
      id: 'delete',
      icon: 'delete',
      label: 'Delete',
    },
  ];
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged(),
  );
  /*
   * Set the focus on this element
   */
  public focus(event?: Event): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   *
   * @param defaultService
   */
  constructor(
    private defaultService: EmailDefaultService,
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private popoverService: UnicaPopoverService,
    private blockActionsService: BlockActionsService,
    private dialogService: UnicaDialogService,
    private translate: TranslateService
  ) {}
  /**
   * Called when a element in the toolbar is selected by the user
   */
  protected performAction({
    item,
    event,
  }: {
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }) {
    switch (item.id) {
      default:
      case 'settings':
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.BUTTON_SETTINGS);
        break;
      case 'link':
        // fire event to open the link popover
        this.openLinkPopover(event);
        break;
      case 'duplicate': {
        this.blockActionsService.duplicateBlock(this);
        break;
      }
      case 'dynamic':
        // fire event to open the rule builder
        this.canvasService.openRuleBuilder({ currentElement: this._block });
        break;
      case 'delete': {
        this.deleteBlock();
        break;
      }
    }
  }

  private async deleteBlock() {
    const confirmed = await this.dialogService.confirm(
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.BLOCK_DELETION_CONFIRMATION'),
      this.translate.instant('MODAL.YES'),
      this.translate.instant('MODAL.NO'),
    );

    if (confirmed) {
      this.blockActionsService.deleteBlock(this);
    }
  }

  /**
   * Get the style of this button
   */
  getStyle(): ElementStyle {
    return this._block.options;
  }

  updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'border' && value && value.border) {
      this.borderSubject.next(<ElementBorder>value.border);
    } else if (attribute === 'color' && value && value.color) {
      this.colorSubject.next(<string>value.color);
    } else if (attribute === 'padding' && value && value.padding) {
      this.innerPaddingSubject.next(<ElementPaddingMargin>value.padding);
    } else if (attribute === 'background' && value && value.background) {
      this.backgroundSubject.next(<ElementBackground>{
        color: value.background.color,
      });
    } else if (attribute === 'lineHeight' && value && value.lineHeight) {
      this.lineHeightSubject.next(<ElementLineHeight>value.lineHeight);
    } else if (attribute === 'margin' && value && value.margin) {
      this.paddingSubject.next(<ElementPaddingMargin>value.margin);
    } else if (attribute === 'font' && value && value.font) {
      this.fontSubject.next(<ElementFont>value.font);
    } else if (
      attribute === 'fullWidth' &&
      value &&
      value.fullWidth !== undefined
    ) {
      // we are checking for undefined because fullWidth can be false
      this.fullWidthSubject.next(<boolean>value.fullWidth);
    } else if (attribute === 'hideOn' && value && value.hideOn !== undefined) {
      // we are checking for undefined because hideOn can be empty string also
      this.hideOnSubject.next(<HideOnType>value.hideOn);
    } else if (attribute === 'align' && value && value.align) {
      this.alignSubject.next(<Align>value.align);
    }
  }

  /**
   * method to return any non style attributes like innerText in button block
   * @returns The element attribute
   */
  getElementAttribute(): ElementAttribute | undefined {
    return {
      innerText: this._block.innerText,
      rules: this._block.options.rules || []
    };
  }

  /**
   * method to update any non style attributes like innerText in button block
   */
  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {
    if (attribute === 'innerText' && value && value.innerText !== undefined) {
      // we are checking for undefined because innerText can be empty string
      // and we need to set it to empty string
      this.innerTextSubject.next(value.innerText);
    } else if (attribute === 'rules' && value && value.rules !== undefined) {
      // we are checking for undefined because rules can be empty array
      this.rulesSubject.next(value.rules);
    }
  }

  /**
   * method to return button block as EmailBlock
   * This is used to get the block in the email canvas
   * @returns _block as EmailBlock
   */
  getCurrentBlock(): EmailBlock | undefined {
    return  this._block;
  }

  /**
   * The innerText of this block
   */
  private innerTextSubject = new BehaviorSubject<string | undefined>(undefined);
  protected innerTextSubject$ = this.innerTextSubject.asObservable().pipe(
    tap((w) => {
      if (this._block) {
        this._block.innerText = w ?? '';
      }
    }),
  );

  /**
   * The border of this block
   */
  private borderSubject = new BehaviorSubject<ElementBorder | undefined>(
    undefined,
  );
  protected borderSubject$ = this.borderSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.border = w;
      }
    }),
  );

  /**
   * The padding of this block
   */
  private paddingSubject = new BehaviorSubject<
    ElementPaddingMargin | undefined
  >(undefined);
  protected paddingSubject$ = this.paddingSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.padding = w;
      }
    }),
  );

  /**
   * The inner padding of button
   */
  private innerPaddingSubject = new BehaviorSubject<
    ElementPaddingMargin | undefined
  >(undefined);
  protected innerPaddingSubject$ = this.innerPaddingSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.innerPadding = w;
      }
    }),
  );

  /**
   * The background of this block
   */
  private backgroundSubject = new BehaviorSubject<
    ElementBackground | undefined
  >(undefined);
  protected backgroundSubject$ = this.backgroundSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.background = { color: w?.color };
      }
    }),
  );

  /**
   * The line height of this block
   */
  private lineHeightSubject = new BehaviorSubject<
    ElementLineHeight | undefined
  >(undefined);
  protected lineHeightSubject$ = this.lineHeightSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.lineHeight = w;
      }
    }),
  );

  /*
   * The color of this block
   */
  private colorSubject = new BehaviorSubject<string | undefined>(undefined);
  protected colorSubject$ = this.colorSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.color = w;
      }
    }),
  );

  /*
   * The font of this block
   */
  private fontSubject = new BehaviorSubject<ElementFont | undefined>(undefined);
  protected fontSubject$ = this.fontSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.font = w;
      }
    }),
  );

  /*
   * The full Width of this block
   */
  private fullWidthSubject = new BehaviorSubject<boolean | undefined>(
    undefined,
  );
  protected fullWidthSubject$ = this.fullWidthSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.fullWidth = w;
      }
    }),
  );

  /*
   * The hide On of this block
   */
  private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
    undefined,
  );
  protected hideOnSubject$ = this.hideOnSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.hideOn = w || undefined;
      }
    }),
  );

  /**
   * The alignment of this block
   */
  private alignSubject = new BehaviorSubject<Align | undefined>(undefined);
  protected alignSubject$ = this.alignSubject.asObservable().pipe(
    tap((align) => {
      if (this._block?.options) {
        this._block.options.align = align;
      }
    }),
  );
  /**
   * * The rules of this block
   */
  private rulesSubject = new BehaviorSubject<IRule[] | undefined>(undefined);
  protected rulesSubject$ = this.rulesSubject.asObservable()
    .pipe(
      tap((rules) => {
        if (this._block?.options) {
          this._block.options.rules = rules;
        }
      })
    );

  /*
   * Opens a popover to allow the user to update the link for the selected block.
   *
   * This method is triggered by a user interaction event (either a mouse click or keyboard event)
   * and opens a popover where the user can enter or modify the URL associated with
   * the specific block.
   *
   * @param event - The event to trigger the popover. It can either be a MouseEvent (click) or
   *                a KeyboardEvent.
   */
  openLinkPopover(event: MouseEvent | KeyboardEvent) {
    if (event instanceof KeyboardEvent && !['Enter', ' '].includes(event.key)) {
      return;
    }
    event.preventDefault();
    const targetElement = event.currentTarget as HTMLElement;
    const popoverInstance =
      this.popoverService.openPopover<AddLinkPopoverFormComponent>(
        AddLinkPopoverFormComponent,
        targetElement,
        {
          // Pass the entire saved object for prepopulating the form
          initialData: this.addLinkFormData ?? {
            redirection: 'url', // default values if none saved
            url: '',
            landingPage: '',
            aliasNameInfo: { name: '', id: '' },
            newWindow: false
          },
          blockName: 'Button',
        },
      );
    if (popoverInstance) {
      popoverInstance.addLinkFormData.pipe(untilDestroyed(this)).subscribe((addLinkFormData: AddLinkFormDataConfig) => {
        // Update your Block JSON here
        if (this._block?.options) {
          this._block.options.redirection = addLinkFormData.redirection;
          this._block.options.url = addLinkFormData.url;
          this._block.options.newWindow = addLinkFormData.newWindow;
          this._block.options.aliasNameInfo = addLinkFormData.aliasNameInfo;
          this._block.options.landingPage = addLinkFormData.redirection === 'lp' ? addLinkFormData.landingPage : '';

          this.addLinkFormData = addLinkFormData;
        }
        this.popoverService.closePopover();
      });
      popoverInstance.closePopover.pipe(untilDestroyed(this)).subscribe(() => {
        this.popoverService.closePopover();
      });
    }
  }
}
