import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  EmailCanvasService,
  EmailHistoryService,
  UnicaEmail,
  UnicaEmailEditorComponent
} from '@hcl/angular/unica-email-editor';
import { UnicaInputComponent } from '@hcl/angular/unica-input';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { BehaviorSubject, distinctUntilChanged } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { DEFAULT_EMAIL } from './angular-email';

@UntilDestroy()
@Component({
  selector: 'angular-lib-email-editor',
  standalone: true,
  imports: [CommonModule, UnicaEmailEditorComponent, UnicaInputComponent,
    UnicaButtonModule, UnicaIconModule, LetDirective],
  // providers: [
  //   {
  //     provide: StructureListingService,
  //     useClass: DeliverStructureListingService
  //   }
  // ],
  templateUrl: './angular-lib-email-editor.component.html',
  styleUrl: './angular-lib-email-editor.component.scss',
})
export class AngularLibEmailEditorComponent {
  /**
   * This is a observable that will tell if, user can execute undo command
   */
  private canUndoSubject = new BehaviorSubject(false);
  protected canUndo$ = this.canUndoSubject.asObservable().pipe(distinctUntilChanged());
  /**
   * This is a observable that will tell if, user can execute undo command
   */
  private canRedoSubject = new BehaviorSubject(false);
  protected canRedo$ = this.canRedoSubject.asObservable().pipe(distinctUntilChanged());
  /**
   * The instance of the history service using which we can perform undo/redo actions
   * this is available after canvas is initialized
   * @private
   */
  private canvasHistoryService: EmailHistoryService | undefined;
  /**
   * The instance of the canvas service
   */
  private canvasService: EmailCanvasService | undefined;
  /**
   * The default email that is loaded in the editor
   * @protected
   */
  protected emailDef: UnicaEmail = DEFAULT_EMAIL;
  /**
   * Called when the canvas is ready
   * @param $event
   */
  protected canvasReady(event: { historyService: EmailHistoryService, canvasService: EmailCanvasService }) {
    this.canvasHistoryService = event.historyService;
    this.canvasService = event.canvasService;
    // register for the undo & redo observable
    this.canvasHistoryService.undoStack$.pipe(untilDestroyed(this))
        .subscribe((list) => this.canUndoSubject.next(list && list.length > 0));
    this.canvasHistoryService.redoStack$.pipe(untilDestroyed(this))
      .subscribe((list) => this.canRedoSubject.next(list && list.length > 0));
  }
  /**
   * Undo the previous canvas action
   * @protected
   */
  protected undo() {
    if (this.canvasHistoryService) {
      this.canvasHistoryService.undo();
    }
  }
  /**
   * Redo the previous canvas action
   * @protected
   */
  protected redo() {
    if (this.canvasHistoryService) {
      this.canvasHistoryService.redo();
    }
  }
  /**
   * This will convert the Editor data to a JSON
   * @protected
   */
  protected generateJson(): void {
    const json = this.canvasService?.getEmailAsJson();
    console.warn(json);
  }
}
