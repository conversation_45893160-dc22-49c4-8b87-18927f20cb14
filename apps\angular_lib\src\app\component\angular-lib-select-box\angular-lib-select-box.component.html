<div class="select-box-container">
  <unica-card>
    <unica-typography [variant]="'text'">
      This is Simple String Array
    </unica-typography>
    <unica-dropdown #stringDropDown [label]="'Sample'" [required]="true" [placeholder]="'Place holder'"
      [hint]="'Please select 1 value'" [errorTranslator]="getErrors.bind(this, 'str')" [options]="optionsString">
    </unica-dropdown>
  </unica-card>

  <unica-card>
    <unica-typography [variant]="'text'">
      This is Simple UnicaOptions Array
    </unica-typography>
    <unica-dropdown [required]="true" [label]="'Sample'" [form]="fc" [multiple]="true" [placeholder]="'Place holder'"
      [hint]="'Please select 1 value'" [errorTranslator]="getErrors.bind(this, 'unicaOpt')" [options]="optionsUnica">
    </unica-dropdown>
  </unica-card>

  <unica-card>
    <unica-typography [variant]="'text'">
      This is Simple custom Object Array
    </unica-typography>
    <unica-dropdown [required]="true" [label]="'Sample'" [placeholder]="'Place holder'" [hint]="'Please select 1 value'"
      [getLabel]="getOptionLabel.bind(this)" [getValue]="getOptionValue.bind(this)"
      [errorTranslator]="getErrors.bind(this, 'obj')" [options]="optionsObject">
    </unica-dropdown>
  </unica-card>

  <unica-card>
    <unica-typography [variant]="'text'">
      This is Simple custom template with options
    </unica-typography>
    <unica-dropdown [label]="'Custom Template'" [options]="optionsCustomTemplate">
      <ng-template #optionTemplate let-option>
        <span>Option: <b><u>{{ option.myLabel }}</u></b></span>
      </ng-template>
    </unica-dropdown>
  </unica-card>
</div>