<div style="width: 100%; height: 100%">
  <div style="padding: 10px">
    <unica-button (clickAction)="addContainer()">
      Add Container
    </unica-button>
  </div>
  <unica-dashboard-builder
    #dashboard
    [options]="dashboardOptions">
    <ng-template unicaTemplate templateName="template0">
      <div>
        Template 0
      </div>
    </ng-template>
    <ng-template unicaTemplate templateName="template1">
      <div>
        Template 1
      </div>
    </ng-template>
    <ng-template unicaTemplate templateName="template2">
      <div>
        Template 2
      </div>
    </ng-template>
  </unica-dashboard-builder>
</div>
