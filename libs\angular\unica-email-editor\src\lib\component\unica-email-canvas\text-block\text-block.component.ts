import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, combineLatest, distinctUntilChanged, map, startWith, tap } from 'rxjs';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { GenerateUniqueId } from '@hcl/unica-common';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { EmailElementType } from '../../../config/element-toolbar';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import {
  ElementFont,
  ElementLineHeight,
  ElementPaddingMargin,
  ElementStyle,
  StyleAttribute,
} from '../../../config/email-common-elements';
import { ElementAttribute } from '../../../config/email-common-elements';
import { EmailBlock, UnicaTextBlock } from '../../../config/email';
import { BlockActionsService } from '../../../service/block-actions.service';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { TranslateService } from '@ngx-translate/core';
import { TextEditorStyles, UnicaTextEditorComponent } from '@hcl/angular/unica-text-editor';
import { ContentChange } from 'ngx-quill';

@Component({
  selector: 'text-block',
  standalone: true,
  imports: [
    CommonModule,
    FocusOverlayComponent,
    UnicaButtonModule,
    UnicaTextEditorComponent,
  ],
  templateUrl: './text-block.component.html',
  styleUrl: './text-block.component.scss',
})
export class TextBlockComponent {
  private _id: string = GenerateUniqueId(13);
  private _dropListId = '';
  private _blockIndex = 0;

  get id(): string {
    return this._dropListId
      ? `${this._dropListId}_${this._blockIndex}`
      : this._id;
  }

  @Input() set dropListId(value: string) {
    this._dropListId = value;
  }

  @Input() set blockIndex(value: number) {
    this._blockIndex = value;
  }

  private _block = new UnicaTextBlock();
  private blockSubject = new BehaviorSubject<UnicaTextBlock>(this._block);

  @Input({
    transform: (value: EmailBlock): UnicaTextBlock => <UnicaTextBlock>value,
  })
  set block(b: UnicaTextBlock) {
    this.blockSubject.next(b);
    this.paddingSubject.next(b.options.padding);
    this.fontSubject.next(b.options.font);
    this.lineHeightSubject.next(b.options.lineHeight);
    this.colorSubject.next(b.options.color);
  }
  /**
   * The padding of this block
   */
  private paddingSubject = new BehaviorSubject<
    ElementPaddingMargin | undefined
  >(undefined);
  protected paddingSubject$ = this.paddingSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.padding = w;
      }
    }),
  );

  private fontSubject = new BehaviorSubject<ElementFont | undefined>(undefined);
  protected fontSubject$ = this.fontSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.font = w;
      }
    }),
  );

  private lineHeightSubject = new BehaviorSubject<
    ElementLineHeight | undefined
  >(undefined);
  protected lineHeightSubject$ = this.lineHeightSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.lineHeight = w;
      }
    }),
  );

  private colorSubject = new BehaviorSubject<string | undefined>(undefined);
  protected colorSubject$ = this.colorSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.color = w;
      }
    }),
  );

  protected block$ = this.blockSubject.asObservable().pipe(
    tap((b) => {
      this._block = b;
    }),
  );

  protected style$ = this.block$.pipe(
    map((b) => {
      return b.options;
    }),
  );

  protected isEmpty$ = this.block$.pipe(
    map((b) => !b.innerText || b.innerText === ''),
  );

  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged(),
  );


  protected combinedStyles$ = combineLatest([
  this.colorSubject$,
  this.lineHeightSubject$,
  this.fontSubject$,
  this.paddingSubject$
]).pipe(
  map(() => {
    const options = this._block.options;
    const { font, lineHeight, color, padding } = options;

    const styles: TextEditorStyles = {
      font: font
        ? {
            family: font.family,
            size: font.size,
            style: font.style,
            weight: typeof font.weight === 'number'
                ? font.weight.toString()
                : font.weight,
          }
        : undefined,
      lineHeight: lineHeight,
      color: color,
      padding: padding
        ? {
            top: padding.top,
            right: padding.right,
            bottom: padding.bottom,
            left: padding.left,
          }
        : undefined
    };

    return styles;
  }),
  startWith(undefined)
);

  protected imageToolbarItems: ElementToolbarItemConfig[] = [
    { id: 'settings', icon: 'settings', label: 'Settings' },
    { id: 'delete', icon: 'delete', label: 'Delete' },
    { id: 'duplicate', icon: 'content_copy', label: 'Duplicate' },
  ];

  constructor(
    private canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private blockActionsService: BlockActionsService,
    private dialogService: UnicaDialogService,
    private translate: TranslateService
  ) {}

  focus(event: Event | undefined): void {
    if (event) {
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }

  openTextPanel() {
    this.toolbarService.openPanel(EmailElementType.TEXT_SETTINGS);
  }

  performAction({
    item,
    event,
  }: {
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }) {
    switch (item.id) {
      case 'settings':
        this.toolbarService.openPanel(EmailElementType.TEXT_SETTINGS);
        break;
      case 'duplicate': {
        this.blockActionsService.duplicateBlock(this);
        break;
      }
      case 'delete': {
        this.deleteBlock();
        break;
      }
    }
  }

  private async deleteBlock() {
    const confirmed = await this.dialogService.confirm(
      this.translate.instant(
        'EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION',
      ),
      this.translate.instant(
        'EMAIL_EDITOR.LABELS.MODAL_MESSAGE.BLOCK_DELETION_CONFIRMATION',
      ),
      this.translate.instant('MODAL.YES'),
      this.translate.instant('MODAL.NO'),
    );

    if (confirmed) {
      this.blockActionsService.deleteBlock(this);
    }
  }

  getStyle(): any {
    return this._block.options;
  }

  updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'font' && value && value.font) {
      this.fontSubject.next(<ElementFont>value.font);
    } else if (attribute === 'padding' && value && value.padding) {
      this.paddingSubject.next(<ElementPaddingMargin>value.padding);
    } else if (attribute === 'lineHeight' && value && value.lineHeight) {
      this.lineHeightSubject.next(<ElementLineHeight>value.lineHeight);
    } else if (attribute === 'color' && value && value.color) {
      this.colorSubject.next(<string>value.color);
    }
  }


  onContentChange(e: ContentChange) {
    this._block.innerText = e.html as string;
  }

  getElementAttribute(): ElementAttribute | undefined {
    return undefined;
  }

  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {}
}
