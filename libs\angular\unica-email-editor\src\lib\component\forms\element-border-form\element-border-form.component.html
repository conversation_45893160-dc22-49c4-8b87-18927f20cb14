<div class="email-editor-form-container">
  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.BORDER_WIDTH' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field">
      <unica-number-spinner
        #width
        [name]="'width'"
        [errorTranslator]="errorTranslator.bind(this, 'EMAIL_EDITOR.LABELS.BORDER_WIDTH')"
        [form]="this.form.get('width')"></unica-number-spinner>
    </div>
  </div>

  <div class="email-editor-form-element" *ngIf="enableRadius">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.BORDER_RADIUS' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field">
      <unica-number-spinner
        [name]="'radius'"
        [errorTranslator]="errorTranslator.bind(this, 'EMAIL_EDITOR.LABELS.BORDER_RADIUS')"
        [form]="this.form.get('radius')"></unica-number-spinner>
    </div>
  </div>

 <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.BORDER_STYLE' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field border-style-width">
      <unica-button-toggle-group
        [elements]="borderStyleToggleElements" [form]="this.form.get('style')" [value]="selectedBorderStyle"
        (valueChange)="selectedBorderStyle = $event[0]">
        <ng-template unicaTemplate [templateName]="'borderStyleTogglerButton'" let-element="element">
          <div style="padding-top:4px;transform: scale(0.7);" [title]="element.label">
            <unica-icon [name]="element.icon"></unica-icon>
          </div>
        </ng-template>
      </unica-button-toggle-group>
    </div>
  </div>

  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{'EMAIL_EDITOR.LABELS.BORDER_COLOR' | translate}}
      </unica-typography>
    </div>
    <div class="email-editor-form-field">
      <unica-color-picker
      #color
      [name]="'color'"
      [errorTranslator]="errorTranslator.bind(this, 'EMAIL_EDITOR.LABELS.BORDER_WIDTH')"
      [form]="this.form.get('color')"></unica-color-picker>
    </div>
  </div>
</div>
