import { applicationConfig, componentWrapperDecorator, Meta, StoryObj } from '@storybook/angular';
import { UnicaTextAreaComponent } from '@hcl/angular/unica-textarea';
import { provideAnimations } from '@angular/platform-browser/animations';

export const ActionsData = {};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaTextAreaComponent> = {
  title: 'Unica Textarea',
  component: UnicaTextAreaComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will be used to render the Textarea across HCL Unica application',
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [provideAnimations()],
    }),
    componentWrapperDecorator(
      (story) => `<div class="light-theme">${story}</div>`,
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      type: 'string',
      description: 'The placeholder that needs to be displayed on textarea'
    },
    disabled :{
      type: 'boolean',
      description: 'The flag based on which the textarea will be enabled or disabled'
    },
    minRows: {
      type: 'number',
      description: 'The min number of rows that need to be displayed in teh UI'
    },
    maxRows: {
      type: 'number',
      description: 'The max number of rows that after which there will be a scroll in the text area'
    }
  },
  args: {},
};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaTextAreaComponent>;

/**
 * The default input box
 */
export const Default: Story = {
  args: {
    label: 'Label'
  },
}
