@import "../../../fonts/fonts.scss";

.unica-email-editor {
  background: var(--unica-bg-page, #ECFEFF);

  .mat-drawer-container {
    background-color: var(--unica-bg-page, #ECFEFF);
    .mat-drawer-content {
      .email-canvas-container {
        .element-active {
          transition: all 0.2s ease;
          box-shadow: 0 0 1px 2px #ff4081;
          position: relative;
        }
        .email-canvas {
          //background-color: white;
          //border: 1px solid #eaeaea;
          overflow: auto;
        }
      }
    }
    .mat-drawer {
      .element-toolbar-container {
        background-color: var(--unica-bg-page, #ECFEFF);
        .element-container {
          border-radius: 4px;
          background: #FFFFFF;
          box-shadow: 0px 1px 2px 0px #0000004D;

          .draggable-element {
            path {
              fill: #292931;
            }
          }
        }
      }
    }
  }
}
// The sub-menu
.editor-sub-menu-container {
  overflow-y: auto;
  .sub-menu-title {
    color: var(--unica-text-tertiary, #5B5B5B);
  }
}
// The settings forms
.email-editor-form-container {
  //min-width: 320px;
  //padding: 10px;

  .email-editor-form-element {
    display: flex;
    min-height: 60px;

    .email-editor-form-label {
      width: 100%;
      padding-top: 10px
    }
    .email-editor-form-field {
      width: 120px;
    }
  }
}
.form-group {
  .form-seperator {
    width: 100%;
    height: 1px;
    padding-top: 8px;
    padding-bottom: 8px;
    border-top: 1px solid var(--unica-outline-card);
  }
}
.drop-active {
  transition: all 0.2s ease;
  background-color: var(--unica-primary, #038D99)!important;
  box-shadow: 0 0 1px 2px var(--unica-primary, #038D99);
  opacity: 0.4;
}
.element-drag-handle,
.inverted-drag-handle {
  background-color: #ff4081;
  color: #fff;
}
.element-toolbar{
  background-color: var(--unica-primary, #038D99)!important;
  color: #fff;
  border-radius: 0px 8px 8px 0px;
  border: 1px solid #FFF;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.30), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}
.structure-toolbar {
  border-radius: 4px;
  color: #fff;
  border: 1px solid #FFF;
  background: var(--unica-primary, #038D99);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.30), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}
.empty-image-place-holder {
  background-color: #038d991a!important;
  color: #7f7f7f !important;
}
.image-place-holder {
  display: flex;
}
.empty-element-container {
  unica-button {
    mat-icon {
      color: rgb(113, 113, 130);
    }
  }
}
//.cdk-drag-animating {
//  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
//}
.structure-hover-element {
  transition: all 0.2s ease;
  box-shadow: 0 0 1px 2px #ff4081;
  position: relative;
}
.element-hover-element {
  transition: all 0.2s ease;
  box-shadow: 0 0 1px 2px #038D99;
  position: relative;
}
.element-preview-container {
  margin-left: -250px;
  border: none;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
            0 8px 10px 1px rgba(0, 0, 0, 0.14),
            0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}
.element-drop-list.cdk-drop-list-dragging .canvas-draggable-element:not(.cdk-drag-placeholder){
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
.structure-min-height {
  min-height: 80px;
}
.canvas-hide-on-icon {
  width: 23px !important;
  height: 23px !important;
}

