import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';

@UntilDestroy()
@Injectable()
export class ElementColorFormService {
  /**
   * Subject that will be fired when the border values are changed
   */
  private valueChangeSubject = new BehaviorSubject<string | undefined>(undefined);
  public valueChange$ = this.valueChangeSubject.asObservable()
  /**
   * the login form
   * @private
   */
  public readonly form: FormGroup;
  /**
   * The validation that we need to add to the form
   * @private
   */
  public validators: {[key:string]: Validators[]} = {
    color: ['', []],
  };
  /**
   * @param formBuilder
   */
  constructor(private formBuilder: FormBuilder) {
    this.form = this.formBuilder.group(this.validators);
    this.form.valueChanges.pipe(untilDestroyed(this)).subscribe((v) => {
      this.valueChangeSubject.next(v.color);
    })
  }
}
