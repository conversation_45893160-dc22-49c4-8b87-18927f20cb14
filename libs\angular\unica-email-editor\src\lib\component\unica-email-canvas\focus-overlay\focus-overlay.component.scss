.focus-overlay {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  z-index: 1;
}

%drag-handle {
  position: absolute;
  cursor: move;
  border-radius: 50% 0 0 50%;
  height: 30px;
  width: 30px;
  font-size: smaller;
  line-height: 0;
}

.element-drag-handle {
  @extend %drag-handle;
  margin-left: -30px;
}
.inverted-drag-handle {
  @extend %drag-handle;
  z-index: 10;
  transform: rotate(180deg);
}
.element-drag-handle,
.inverted-drag-handle {
  .icon {
    position: relative;
    top: 9%;
    left: 19%;
    ::ng-deep svg {
      fill: currentColor;
    }
  }
}
.element-toolbar {
  position: absolute;
  cursor: pointer;
  margin-left: 3px;
  width: 30px;
  font-size: smaller;
  line-height: 0;
  padding: 5px;
  margin-right: -32px;
  float: right;
  .toolbar-item {
    height: 25px;
  }
}

.structure-toolbar {
  display: flex;
  cursor: pointer;
  font-size: smaller;
  line-height: 0;
  padding: 3px 3px 3px 7px;
  margin-top: -10px;
}

.hide-on-icon-tool {
  position: absolute;
  width: 32px;
  font-size: smaller;
  padding: 5px;
  right: 0px;
  top: -37px;
  background-color: var(--unica-primary, #038d99) !important;
  z-index: 10;
  color: #f9f9f9 !important;
}
