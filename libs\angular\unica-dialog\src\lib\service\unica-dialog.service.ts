import { Injectable } from '@angular/core';
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { UnicaDialogComponent } from '../unica-dialog/unica-dialog.component';
import { ModalConfig } from '../unica-dialog/unica-dialog.model';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class UnicaDialogService {
  constructor(
    private dialog: MatDialog,
    private translate: TranslateService,
  ) {}

  /**
   * Opens a dialog with the UnicaDialogComponent
   * @param config The configuration for the dialog
   * @returns A reference to the dialog
   */
  open(config: ModalConfig): MatDialogRef<UnicaDialogComponent> {
    const dialogConfig = new MatDialogConfig();

    // Apply the modal config to the dialog config
    dialogConfig.disableClose = config.disableClose ?? false;
    dialogConfig.width = config.width;
    dialogConfig.height = config.height;
    dialogConfig.data = config;
    dialogConfig.backdropClass = config.backdropClass;
    dialogConfig.hasBackdrop = config.hasBackdrop ?? true;

    // Always add our custom panel class
    dialogConfig.panelClass = ['unica-dialog-panel'];

    // Add resizable class if needed
    if (config.resizeable) {
      dialogConfig.panelClass.push('dialog-container');
    }

    // Add any additional custom classes
    if (config.styleClass) {
      dialogConfig.panelClass.push(config.styleClass);
    }

    // Add container class if provided
    if (config.containerClass) {
      dialogConfig.panelClass.push(config.containerClass);
    }

    return this.dialog.open(UnicaDialogComponent, dialogConfig);
  }

  /**
   * Opens a confirmation dialog
   * @param title The title of the dialog
   * @param message The message to display
   * @param okLabel The label for the OK button
   * @param cancelLabel The label for the cancel button
   * @returns A promise that resolves to true if the user clicks OK, false otherwise
   */
  confirm(
    title: string,
    message: string,
    okLabel: string,
    cancelLabel: string,
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const dialogRef = this.open({
        title: title ? title : '',
        content: message ? message : '',
        confirmActionTemplate: true,
        okLabel: okLabel ? okLabel : '',
        cancelButton: cancelLabel ? cancelLabel : '',
        okCallback: () => {
          resolve(true);
          dialogRef.close();
        },
        cancelCallback: () => {
          resolve(false);
          dialogRef.close();
        },
      });

      dialogRef.afterClosed().subscribe(() => {
        resolve(false);
      });
    });
  }

  /**
   * Opens an alert dialog
   * @param title The title of the dialog
   * @param message The message to display
   * @param okLabel The label for the OK button
   * @returns A promise that resolves when the dialog is closed
   */
  alert(title: string, message: string, okLabel: string): Promise<void> {
    return new Promise((resolve) => {
      const dialogRef = this.open({
        title,
        content: message,
        alertActionTemplate: true,
        okLabel: okLabel ? okLabel : '',
        okCallback: () => {
          resolve();
          dialogRef.close();
        },
      });

      dialogRef.afterClosed().subscribe(() => {
        resolve();
      });
    });
  }
}
