<focus-overlay
  #focusOverLay
  (click)="focus($event)"
  [items]="imageToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodePadding]="(paddingSubject$ | async) ?? null"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false"
>
  <unica-text-editor
    [content]="(block$ | async)?.innerText || (block$ | async)?.label || ''"
    [styles]="(combinedStyles$ | async) ?? undefined"
    (contentChange)="onContentChange($event)"
    [required]="true"
    [readOnly]="false"
  ></unica-text-editor>
</focus-overlay>
