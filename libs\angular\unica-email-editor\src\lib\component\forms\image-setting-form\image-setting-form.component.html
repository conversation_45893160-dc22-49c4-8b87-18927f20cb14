<div class="form-group" *ngrxLet="elementStyle$; let style">

  <!--The image src form-->
  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{ 'EMAIL_EDITOR.LABELS.IMAGE_SOURCE' | translate }}
      </unica-typography>
    </div>
    <div class="email-editor-form-label">
      <unica-button
          (clickAction)="browseImageFromContentPicker()"
          [type]="'button'"
          [width]="'180px'">
          {{ 'EMAIL_EDITOR.LABELS.BROWSE_IMAGE' | translate }}
      </unica-button>
    </div>
    <div class="email-editor-form-field"  *ngrxLet="elementAttribute$; let attribute">
      <unica-input [form]="this.form.get('src')" (valueEnteredEvent)="externalImageEntered()"></unica-input>
    </div>
  </div>

  <!--The dimension form-->
  <ng-container *ngIf="style?.width">
    <element-height-form #heightFormComponent [enableAuto]="true"></element-height-form>
    <element-width-form #widthFormComponent [enableAuto]="true"></element-width-form>
    <div class="form-seperator"></div>
    
    <element-alignment-form></element-alignment-form>
    <div class="form-seperator"></div>

    <element-padding-form></element-padding-form>
    <div class="form-seperator"></div>

    <element-border-form></element-border-form>
    <div class="form-seperator"></div>

    <element-hide-on-form></element-hide-on-form>
  </ng-container>
</div>
