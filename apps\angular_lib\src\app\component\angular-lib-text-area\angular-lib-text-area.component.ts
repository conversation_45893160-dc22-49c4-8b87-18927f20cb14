import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {UnicaTextAreaComponent} from '@hcl/angular/unica-textarea';

@Component({
  selector: 'angular-lib-text-area',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    UnicaTextAreaComponent
  ],
  templateUrl: './angular-lib-text-area.component.html',
  styleUrl: './angular-lib-text-area.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AngularLibTextAreaComponent {}