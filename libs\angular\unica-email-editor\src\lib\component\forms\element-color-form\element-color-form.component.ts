import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { UnicaColorPickerComponent, UnicaNumberSpinnerComponent } from '@hcl/angular/unica-input';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { BehaviorSubject, distinctUntilChanged, tap } from 'rxjs';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ElementColorFormService } from '../../../service/form/element-color-form.service';

@UntilDestroy()
@Component({
  selector: 'element-color-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe,  UnicaTypographyComponent, UnicaColorPickerComponent],
  providers: [ElementColorFormService],
  templateUrl: './element-color-form.component.html',
  styleUrl: './element-color-form.component.scss',
})
export class ElementColorFormComponent {
  /**
   * Get the style of the current element
   */
  private elementStyle$ = this.canvasService.styleOfFocusedElement$
  /**
   * The color
   */
  private _color: string | undefined;
  private colorSubject = new BehaviorSubject<string | undefined>(undefined);
  protected color$ = this.colorSubject.asObservable().pipe(tap(x => this._color = x));
  @Input() set color(v: string | undefined) {
    this.colorSubject.next(v);
    if (v) {
      this.form.setValue({ color: v });
    }
  }
  /**
   * the color Form Group
   */
  protected readonly form: FormGroup;
  /**
   * Default constructor
   */
  constructor(private colorForm: ElementColorFormService,
              private translate: TranslateService,
              private canvasService: EmailCanvasService) {
    this.form = this.colorForm.form;
    this.elementStyle$.pipe(untilDestroyed(this)).subscribe((s) => {
      this.color = s?.color;
    });
    // tell the canvas service to change
    this.colorForm.valueChange$.pipe(distinctUntilChanged((previous, current) => {
      if (previous === current)
        return true;
      return false;
    }),tap((v) => { }),
    untilDestroyed(this)).subscribe((v) => {
      this.canvasService.updatedFocusedElementStyle('color', {color: v});
    });
  }
  /**
   * Handle the errors on the fields
   * @protected
   */
  protected errorTranslator(field: string,error: ValidationErrors): string {
    if (error) {
      const errorList = Object.keys(error);
      if(errorList.length > 0) {
        // the 0th key represents the error
        return this.translate.instant('UNICA_COMMON.ERRORS.' + errorList[0].toUpperCase(),
          { field: this.translate.instant(field), ...error });
      }
    }
    return '';
  }
}
