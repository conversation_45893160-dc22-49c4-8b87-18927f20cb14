<div class="unica-email-editor">
  <!--We have used mat-drawer-container but if the layout needs to be changed then this section needs to be updated-->
  <mat-drawer-container autosize>
    <!--Drawer is the strip that moves in or out-->
    <mat-drawer #drawer
                class="drawer"
                [position]="elementToolbarPosition === 'left' ? 'start' : 'end'"
                [mode]="'side'"
                [disableClose]="true"
                [opened]="true">
      <!-- Drag handle for repositioning the drawer -->
      <div class="drawer-drag-handle"
           cdkDrag
           [cdkDragBoundary]="'.mat-drawer-container'"
           [cdkDragLockAxis]="'x'"
           (cdkDragEnded)="onDrawerDragEnded($event)"
           (cdkDragMoved)="onDrawerDragMoved($event)">
        <div class="drag-handle-indicator">
          <div class="drag-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <email-editor-element-toolbar
        [position]="elementToolbarPosition">
      </email-editor-element-toolbar>
    </mat-drawer>

    <!--The actual canvas resides in the content-->
    <mat-drawer-content>
      <unica-email-canvas></unica-email-canvas>
    </mat-drawer-content>
  </mat-drawer-container>
</div>

<!--Teh sub menu-->
<ng-container
  *ngrxLet="subMenuService.activateSubMenu$; let activeSubMenu">
  <ng-container
    *ngIf="activeSubMenu">
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOpen]="activeSubMenu ? true :  false"
      [cdkConnectedOverlayOrigin]="activeSubMenu.trigger"
      [cdkConnectedOverlayPositions]="overlayPosition"
      (attach)="onOverlayAttach()"
      (detach)="onOverlayDetach()"
    >
      <div
        class="element-toolbar-sub-menu"
        [ngStyle]="{'display': (this.toolbarService.dragStart$ | async) ? 'none' : 'block' }">
        <email-editor-sub-menu
          [type]="activeSubMenu.type"></email-editor-sub-menu>
      </div>

    </ng-template>
  </ng-container>
</ng-container>
