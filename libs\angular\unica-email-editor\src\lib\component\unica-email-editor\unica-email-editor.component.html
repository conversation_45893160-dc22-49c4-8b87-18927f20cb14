<div class="unica-email-editor">
  <!--We have used mat-drawer-container but if the layout needs to be changed then this section needs to be updated-->
  <mat-drawer-container autosize>
    <!-- Drop zone placeholders inside the container -->
    <div class="drop-zone-left visible"
         style="background: red !important; border: 3px solid blue !important; z-index: 99999 !important;"
         [class.active]="isDraggingToolbar && elementToolbarPosition !== 'left'"
         (dragover)="onDropZoneDragOver($event, 'left')"
         (drop)="onDropZoneDrop($event, 'left')"
         (dragenter)="onDropZoneDragOver($event, 'left')">
      <div class="drop-zone-indicator">
        <div class="drop-zone-icon">←</div>
        <div class="drop-zone-text">LEFT DROP ZONE</div>
      </div>
    </div>

    <div class="drop-zone-right visible"
         style="background: green !important; border: 3px solid yellow !important; z-index: 99999 !important;"
         [class.active]="isDraggingToolbar && elementToolbarPosition !== 'right'"
         (dragover)="onDropZoneDragOver($event, 'right')"
         (drop)="onDropZoneDrop($event, 'right')"
         (dragenter)="onDropZoneDragOver($event, 'right')">
      <div class="drop-zone-indicator">
        <div class="drop-zone-icon">→</div>
        <div class="drop-zone-text">RIGHT DROP ZONE</div>
      </div>
    </div>
    <!--Drawer is the strip that moves in or out-->
    <mat-drawer #drawer
                class="drawer"
                [position]="elementToolbarPosition === 'left' ? 'start' : 'end'"
                [mode]="'side'"
                [disableClose]="true"
                [opened]="true">
      <email-editor-element-toolbar
        [position]="elementToolbarPosition"
        [isDraggable]="true"
        (positionChange)="onToolbarPositionChange($event)"
        (dragStateChange)="onToolbarDragStateChange($event)">
      </email-editor-element-toolbar>
    </mat-drawer>

    <!--The actual canvas resides in the content-->
    <mat-drawer-content>
      <unica-email-canvas></unica-email-canvas>
    </mat-drawer-content>
  </mat-drawer-container>
</div>

<!--Teh sub menu-->
<ng-container
  *ngrxLet="subMenuService.activateSubMenu$; let activeSubMenu">
  <ng-container
    *ngIf="activeSubMenu">
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOpen]="activeSubMenu ? true :  false"
      [cdkConnectedOverlayOrigin]="activeSubMenu.trigger"
      [cdkConnectedOverlayPositions]="overlayPosition"
      (attach)="onOverlayAttach()"
      (detach)="onOverlayDetach()"
    >
      <div
        class="element-toolbar-sub-menu"
        [ngStyle]="{'display': (this.toolbarService.dragStart$ | async) ? 'none' : 'block' }">
        <email-editor-sub-menu
          [type]="activeSubMenu.type"></email-editor-sub-menu>
      </div>

    </ng-template>
  </ng-container>
</ng-container>
