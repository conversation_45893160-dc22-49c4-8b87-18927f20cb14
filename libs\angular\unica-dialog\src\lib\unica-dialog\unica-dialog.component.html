<section class="unica-modal" [class]="data.styleClass">
  <div *ngIf="!data.resizeable" mat-dialog-title>
    <ng-container
      *ngTemplateOutlet="data.titleTemplate ? data.titleTemplate : title"
    ></ng-container>
  </div>

  <div
    *ngIf="data.resizeable"
    mat-dialog-title
    cdkDrag
    cdkDragRootElement=".cdk-overlay-pane"
    cdkDragHandle
    class="dragHold"
  >
    <ng-container
      *ngTemplateOutlet="data.titleTemplate ? data.titleTemplate : title"
    ></ng-container>
  </div>

  <ng-template #title>
    <h1 mat-dialog-title>{{ data.title }}</h1>
  </ng-template>
  <div mat-dialog-content>
    <ng-container
      [ngTemplateOutlet]="data.contentTemplate ? data.contentTemplate : content"
      [ngTemplateOutletContext]="{ data: data.data }"
    ></ng-container>
  </div>
  <div mat-dialog-actions>
    <ng-container
      [ngTemplateOutlet]="
        data.actionsTemplate
          ? data.actionsTemplate
          : inBuildActionTemplateName || empty
      "
      [ngTemplateOutletContext]="{ data: data.data }"
    ></ng-container>
  </div>
  <ng-template #content>
    {{ data.content }}
  </ng-template>
  <ng-template #contentHtml>
    <div [innerHTML]="data.content"></div>
  </ng-template>
  <ng-template #empty> </ng-template>
  <ng-template #confirmActionTemplate>
    <div class="template-actions">
      <unica-button
        type="submit"
        (clickAction)="data.okCallback && data.okCallback()"
        class="confirm-button"
        >{{ 'MODAL.OK' | translate }}</unica-button
      >
      <unica-button
        type="button"
        (clickAction)="data.cancelCallback && data.cancelCallback()"
        class="cancel-button"
        >{{ 'MODAL.CANCEL' | translate }}</unica-button
      >
    </div>
  </ng-template>
  <ng-template #alertActionTemplate>
    <div class="template-actions">
      <unica-button
        type="submit"
        (clickAction)="data.okCallback && data.okCallback()"
        class="confirm-button"
        >{{ 'MODAL.CONFIRM' | translate }}</unica-button
      >
    </div>
  </ng-template>
</section>
