import { Injectable } from '@angular/core';
import { EmailCanvasService } from './email-canvas.service';
import { EmailBlock } from '../config/email';

/**
 * Interface for block components that can be duplicated or deleted
 */
export interface BlockComponent {
  id: string;
  block: EmailBlock;
}

/**
 * Service providing common block actions (duplicate, delete)
 * Eliminates code duplication across block components
 */
@Injectable()
export class BlockActionsService {

  constructor(private canvasService: EmailCanvasService) {}

  /**
   * Duplicate a block component
   * Tries position-based ID first, falls back to component reference matching
   * @param blockComponent The block component to duplicate
   */
  duplicateBlock(blockComponent: BlockComponent): void {
    // Try position-based ID first (new format: dropListId_blockIndex)
    const idParts = blockComponent.id.split('_');
    if (idParts.length >= 2) {
      const dropListId = idParts[0];
      const blockIndex = parseInt(idParts[1]);

      if (!isNaN(blockIndex)) {
        this.canvasService.duplicateBlockById(dropListId, blockIndex, true);
        return;
      }
    }

    // Fallback to finding block by component reference (for saved layouts)
    this.canvasService.duplicateBlockByComponent(blockComponent, true);
  }

  /**
   * Delete a block component
   * Tries position-based ID first, falls back to component reference matching
   * @param blockComponent The block component to delete
   */
  deleteBlock(blockComponent: BlockComponent): void {
    // Try position-based ID first (new format: dropListId_blockIndex)
    const idParts = blockComponent.id.split('_');
    if (idParts.length >= 2) {
      const dropListId = idParts[0];
      const blockIndex = parseInt(idParts[1]);

      if (!isNaN(blockIndex)) {
        this.canvasService.removeBlockById(dropListId, blockIndex, true);
        return;
      }
    }

    // Fallback to finding block by component reference (for saved layouts)
    this.canvasService.removeBlockByComponent(blockComponent, true);
  }
}
