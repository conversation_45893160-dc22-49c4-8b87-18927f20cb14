.link-popover-container {
  background: white;
  border-radius: 8px;
  width: 340px;
  // height: 112px;
  padding: 22px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: height 0.2s ease;

  &.expanded {
    // height: 140px;
  }

  .link-popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 18px;

    .link-popover-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 100%;
      letter-spacing: 0.5px;
      color: #1D1D23;
    }

    .close-btn {
      height: 20px;
      width: 20px;
      display: flex;
      padding: 0;
      color: #717182;
    
      mat-icon {
        font-size: 18px;
      }
    }
  }
  
  .link-popover-content {
    display: flex;
    flex-direction: column;
    // gap: 12px;

    .full-width {
      width: 100%;
    }
    
    .save-btn {
      height: 30px;
      font-size: 14px;
      border-radius: 4px;
    }
    
    .browse-btn {
      width: 100px;
      margin-bottom: 20px;
    }
  }
}