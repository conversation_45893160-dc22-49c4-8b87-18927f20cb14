<focus-overlay
  #focusOverLay
  (click)="focus($event)"
  *ngrxLet="style$; let style"
  [items]="dividerToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
  [nodePadding]="(paddingSubject$ | async) ?? null"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false" style="position: relative;">
  <div *ngrxLet="block$; let block" style="display: table;width: 100%;">
    <div [elementBorder]="(borderSubject$ | async) ?? null" rule="borderTop"></div>
  </div>
</focus-overlay>
