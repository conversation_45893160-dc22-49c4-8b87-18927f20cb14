import { ChangeDetectionStrategy, Component, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ElementWidthFormComponent } from '../element-width-form/element-width-form.component';
import { LetDirective } from '@ngrx/component';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { ElementHeightFormComponent } from '../element-height-form/element-height-form.component';
import { ElementPaddingFormComponent } from '../element-padding-form/element-padding-form.component';
import { ElementAlignmentFormComponent } from '../element-alignment-form/element-alignment-form.component';
import { ElementBorderFormComponent } from '../element-border-form/element-border-form.component';
import { ElementHideOnFormComponent } from '../element-hide-on-form/element-hide-on-form.component';
import { <PERSON><PERSON><PERSON><PERSON>, Form<PERSON>roup, Validator, Valida<PERSON> } from '@angular/forms';
import { Until<PERSON><PERSON><PERSON>, untilDestroyed } from '@ngneat/until-destroy';
import { distinctUntilChanged } from 'rxjs';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UnicaInputComponent } from '@hcl/angular/unica-input';
import { TranslatePipe } from '@ngx-translate/core';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaImageBlock } from '../../../config/email';

@UntilDestroy()
@Component({
  selector: 'image-setting-form',
  standalone: true,
  imports: [CommonModule, ElementWidthFormComponent, TranslatePipe, LetDirective, ElementHeightFormComponent, ElementPaddingFormComponent, ElementAlignmentFormComponent, ElementBorderFormComponent, ElementHideOnFormComponent, UnicaTypographyComponent, UnicaButtonModule, UnicaInputComponent],
  templateUrl: './image-setting-form.component.html',
  styleUrl: './image-setting-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ImageSettingFormComponent {
  /**
   * Get the style of the current element
   */
  protected elementStyle$ = this.canvasService.styleOfFocusedElement$;
  /**
   * Get the attribute of the current element
   */

  protected elementAttribute$ = this.canvasService.attributeOfFocusedElement$;
  protected currentElement$ = this.canvasService.currentBlockElement$;
  
  /**
   * The validation that we need to add to the form
   * @private
   */
  public validators: {[key:string]: Validators[]} = {
    src: ['', []]
  };
  /**
   * the Form Group to store any form attributes
   */
  form: FormGroup;
  block: UnicaImageBlock | undefined;
  @ViewChild('widthFormComponent') widthFormComponent: ElementWidthFormComponent | undefined;
  @ViewChild('heightFormComponent') heightFormComponent: ElementHeightFormComponent | undefined;
  /**
   * Default constructor
   */
  constructor(private canvasService: EmailCanvasService,
    private formBuilder: FormBuilder
  ) {
    this.form = this.formBuilder.group(this.validators);
    this.elementAttribute$.pipe(untilDestroyed(this)).subscribe((s) => {
      this.form.get('src')?.setValue(s?.src);
    });
    this.form.valueChanges.pipe(distinctUntilChanged((previous, current) => {
      if (previous && current && previous.src === current.src)
        return true;
      return false;
    }),untilDestroyed(this)).subscribe((v) => {
      this.canvasService.updatedFocusedElementAttribute('src',  v);
      this.getImageDimenstion(v.src);
    });
    this.currentElement$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (s) {
        this.block = s as UnicaImageBlock;
      }
    });
  }

  browseImageFromContentPicker() {
    this.canvasService.openContentPicker();
  }

  externalImageEntered() {
    this.canvasService.updatedFocusedElementAttribute('downloadDetails',  { downloadDetails: null });
  }

  getImageDimenstion(imgUrl: string){
    let img = new Image();
    img.src = imgUrl;
    img.onload = (event) => {
      let loadedImage = event.currentTarget as HTMLImageElement;
      if (loadedImage) {
        this.widthFormComponent?.updateWidthInForm({ ...this.block?.options.width, value: loadedImage.width, unit: this.block?.options?.width?.unit || 'px' });
        this.heightFormComponent?.updateHeightInForm({ ...this.block?.options.height, value: loadedImage.height, unit: this.block?.options?.height?.unit || 'px' });
      }
    };
  }
}
