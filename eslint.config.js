const nx = require('@nx/eslint-plugin');

module.exports = [
  ...nx.configs['flat/base'],
  ...nx.configs['flat/typescript'],
  ...nx.configs['flat/javascript'],
  {
    ignores: ['**/dist'],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    rules: {
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'error', // dont use any
      'no-console': 'warn', // warning if u use console.log
      '@nx/enforce-module-boundaries': [
        // avoid circular dependencies
        'error',
        {
          enforceBuildableLibDependency: true,
          allow: ['^.*/eslint(\\.base)?\\.config\\.[cm]?js$'],
          depConstraints: [
            {
              sourceTag: 'app:platform-web',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular',
                'lib:angular-button',
                'lib:angular-login',
                'lib:angular-input',
                'lib:angular-accordion',
                'lib:angular-snack-bar',
                'lib:angular-app-bar',
                'lib:angular-date-picker',
              ],
            },
            {
              sourceTag: 'app:detect-ui',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular',
                'lib:angular-app-bar',
                'lib:angular-input',
              ],
            },
            {
              sourceTag: 'app:angular-lib',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular',
                'lib:shared',
                'lib:angular-button',
                'lib:angular-login',
                'lib:angular-common',
                'lib:angular-input',
                'lib:angular-icon',
                'lib:angular-typography',
                'lib:angular-panel',
                'lib:angular-date-picker',
                'lib:angular-app-bar',
                'lib:angular-date-picker',
                'lib:angular-unica-dropdown',
                'lib:angular-menu',
                'lib:angular-textarea',
                'lib:angular-email-editor',
              ],
            },
            {
              sourceTag: 'app:cdp',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular',
                'lib:shared',
                'lib:angular-button',
                'lib:angular-login',
                'lib:angular-common',
                'lib:angular-input',
                'lib:angular-icon',
                'lib:angular-typography',
                'lib:angular-panel',
                'lib:angular-snack-bar',
                'lib:angular-date-picker',
                'lib:angular-app-bar',
                'lib:angular-menu',
              ],
            },
            {
              sourceTag: 'lib:angular-email-editor',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-common',
                'lib:angular-icon',
                'lib:angular-button',
                'lib:angular-typography',
                'lib:angular-input',
                'lib:angular-dialog',
                'lib:angular',
                'lib:angular-text-editor'
              ],
            },
            {
              sourceTag: 'lib:angular-textarea',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:angular-common'],
            },
            {
              sourceTag: 'lib:angular-menu',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-common',
                'lib:angular-icon',
                'lib:angular-button',
                'lib:angular-typography',
              ],
            },
            {
              sourceTag: 'lib:angular-unica-dropdown',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-common',
                'lib:angular-input',
                'lib:angular-unica-textarea',
              ],
            },
            {
              sourceTag: 'app:detect',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:angular'],
            },
            {
              sourceTag: 'app:react-lib',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:react-*'],
            },
            {
              sourceTag: 'lib:angular-common',
              onlyDependOnLibsWithTags: ['lib:shared'],
            },
            {
              sourceTag: 'lib:angular-app-bar',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:common',
                'lib:angular-typography',
                'lib:angular-icon',
              ],
            },
            {
              sourceTag: 'lib:angular-icon',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-typography',
              ],
            },
            {
              sourceTag: 'lib:angular-input',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-icon',
                'lib:angular-common',
              ],
            },
            {
              sourceTag: 'lib:angular-typography',
              onlyDependOnLibsWithTags: ['lib:shared'],
            },
            {
              sourceTag: 'lib:angular-login',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-button',
                'lib:angular-login',
                'lib:angular-common',
                'lib:angular-input',
                'lib:angular-icon',
                'lib:angular-typography',
                'lib:angular-panel',
              ],
            },
            {
              sourceTag: 'lib:angular-side-nav',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-icon',
                'lib:angular-typography',
                'lib:angular-common',
                'lib:angular-accordion',
              ],
            },
            {
              sourceTag: 'lib:angular-snack-bar',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-common',
                'lib:angular-icon',
                'lib:angular-typography',
              ],
            },
            {
              sourceTag: 'lib:angular-accordion',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-common',
                'lib:angular-icon',
                'lib:angular-typography',
              ],
            },
            {
              sourceTag: 'lib:angular-dashboard-builder',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:angular-common'],
            },
            {
              sourceTag: 'lib:angular-button',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:angular-common'],
            },
            {
              sourceTag: 'lib:angular-common',
              onlyDependOnLibsWithTags: ['lib:shared'],
            },
            {
              sourceTag: 'lib:shared',
              onlyDependOnLibsWithTags: [],
            },
            {
              sourceTag: 'lib:angular-panel',
              onlyDependOnLibsWithTags: [],
            },
            {
              sourceTag: 'lib:angular-date-picker',
              onlyDependOnLibsWithTags: ['lib:shared', 'lib:angular-common'],
            },
            {
              sourceTag: 'lib:angular-dialog',
              onlyDependOnLibsWithTags: [
                'lib:shared',
                'lib:angular-button',
                'lib:angular-common',
              ],
            },
          ],
        },
      ],
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    // Override or add rules here
    rules: {},
  },
  {
    files: ['**/*.json'],
    rules: {
      '@nx/dependency-checks': [
        'error',
        {
          ignoredFiles: ['{projectRoot}/eslint.config.{js,cjs,mjs}'],
        },
      ],
    },
    languageOptions: {
      parser: require('jsonc-eslint-parser'),
    },
  },
  {
    files: ['**/*.json'],
    rules: {
      '@nx/dependency-checks': [
        'error',
        {
          ignoredFiles: ['{projectRoot}/eslint.config.{js,cjs,mjs}'],
        },
      ],
    },
    languageOptions: {
      parser: require('jsonc-eslint-parser'),
    },
  },
  {
    files: ['**/*.json'],
    rules: {
      '@nx/dependency-checks': [
        'error',
        {
          ignoredFiles: ['{projectRoot}/eslint.config.{js,cjs,mjs}'],
        },
      ],
    },
    languageOptions: {
      parser: require('jsonc-eslint-parser'),
    },
  },
  {
    files: ['**/*.json'],
    rules: {
      '@nx/dependency-checks': [
        'error',
        {
          ignoredFiles: ['{projectRoot}/eslint.config.{js,cjs,mjs}'],
        },
      ],
    },
    languageOptions: {
      parser: require('jsonc-eslint-parser'),
    },
  },
  {
    files: ['**/*.json'],
    rules: {
      '@nx/dependency-checks': [
        'error',
        {
          ignoredFiles: ['{projectRoot}/eslint.config.{js,cjs,mjs}'],
        },
      ],
    },
    languageOptions: {
      parser: require('jsonc-eslint-parser'),
    },
  },
];
