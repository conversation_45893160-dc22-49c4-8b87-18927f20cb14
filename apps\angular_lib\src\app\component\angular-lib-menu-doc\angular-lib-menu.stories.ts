import {
  applicationConfig,
  componentWrapperDecorator,
  Meta,
  moduleMetadata,
  StoryObj
} from '@storybook/angular';
import { UnicaMenuComponent, UnicaMenuItemComponent } from '@hcl/angular/unica-menu';
import { provideAnimations } from '@angular/platform-browser/animations';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaAvatarComponent, UnicaIconModule } from '@hcl/angular/unica-icon';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';

export const ActionsData = {};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaMenuComponent> = {
  title: 'Unica Menu',
  component: UnicaMenuComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will be used to render the drop-down menu across HCL Unica application',
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [provideAnimations()],
    }),
    moduleMetadata({
      imports: [UnicaMenuItemComponent, UnicaButtonModule,
        UnicaAvatarComponent, UnicaIconModule, UnicaTypographyComponent]
    }),
    componentWrapperDecorator(
      (story) =>
                `<div class="light-theme">
                  ${story}
                 </div>`
    )],
  tags: ['autodocs'],
  argTypes: {
    menuTrigger: {
      type: 'string',
      description: 'The element on which the menu should open should have \'menuTrigger\' directive. In' +
        ' the example the div tag has this directive, the library identifies a click on this element &' +
        ' opens the menu'
    }
  },
  args: {
  },
};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaMenuComponent>;


/**
 * The default input box
 */
export const ButtonMenu: Story = {
  args: {},
  render: (args) => ({
    props: args,
    template: `
      <unica-menu>
        <!--The menu trigger-->
        <div menuTrigger>
          <unica-button [variant]="'link'" style="width: 100px; display: block">
            <div class="unica-user-menu"
                 style="display: flex; gap: 10px">
              <unica-typography [variant]="'text'">
                Button
              </unica-typography>
              <unica-icon [name]="'arrow_drop_down'"></unica-icon>
            </div>
          </unica-button>
        </div>
        <div class="user-menu-panel">
          <unica-menu-item>Item 1</unica-menu-item>
          <unica-menu-item>Item 2</unica-menu-item>
        </div>
      </unica-menu>
  `,
  }),
}

/**
 * The default input box
 */
export const Default: Story = {
  args: {},
  render: (args) => ({
    props: args,
    template: `
      <unica-menu>
        <div menuTrigger>
          Unica Menu
        </div>
        <unica-menu-item>Item 1</unica-menu-item>
        <unica-menu-item>Item 2</unica-menu-item>
      </unica-menu>
  `,
  }),
}

