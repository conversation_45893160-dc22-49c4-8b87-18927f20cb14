import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LetDirective } from '@ngrx/component';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { DomSanitizer } from '@angular/platform-browser';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { UnicaButtonBlock } from '../../../config/email';
import { IRule } from '../../../config/email-common-elements';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { UnicaButtonModule } from '@hcl/angular/unica-button';

@UntilDestroy()
@Component({
  selector: 'button-dynamic-setting-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaIconModule, UnicaTypographyComponent, UnicaButtonModule, LetDirective],
  templateUrl: './button-dynamic-setting-form.component.html',
  styleUrl: './button-dynamic-setting-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ButtonDynamicSettingFormComponent {
  /**
   * Get the attribute of the current element
   */
  protected elementAttribute$ = this.canvasService.attributeOfFocusedElement$;
  protected currentElement$ = this.canvasService.currentBlockElement$;
  rules: IRule[] = [];
  block: UnicaButtonBlock | undefined;
  /**
   * Default constructor
   */
  constructor(private canvasService: EmailCanvasService, 
    private dialogService: UnicaDialogService,
    private translate: TranslateService,
    private sanitizer: DomSanitizer) {
    this.elementAttribute$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (s) {
        this.rules = s.rules ? s.rules : [];
      }
    });
    this.currentElement$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (s) {
        this.block = s as UnicaButtonBlock;
      }
    });
  }

  async actionClicked(event: any, index: number) {
    const ruleInfo = this.rules && this.rules?.length > 0 ? this.rules[index] : undefined;
    if (event === 'edit') {
      if (ruleInfo) {
        this.canvasService.openRuleBuilder({ 
          currentElement: this.block, 
          ruleInfo: ruleInfo
        });
      }
    } else if (event === 'delete') {
      const confirmed = await this.dialogService.confirm(
        this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
        this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.RULE_DELETION_CONFIRMATION', { 0: ruleInfo?.name }),
        this.translate.instant('MODAL.YES'),
        this.translate.instant('MODAL.NO'),
      );
      if (confirmed) {
        this.rules?.splice(index, 1);
        this.canvasService.updatedFocusedElementAttribute('rules', { rules: this.rules ? this.rules : []});
      }
    }
  }

  openLinkInNewTab(url  : string | undefined): void {
    if (url) {
      window.open(url, "_blank");
    }
  }

  /**
   * Get the information of the main button block
   * and convert it into a default rule info object
   * @returns IRule type object
   */
  getDefaultRuleInfo(): IRule {
    return {
      buttonText: this.block?.innerText || '',
      isDefault: true,
      name: this.translate.instant('EMAIL_EDITOR.LABELS.DEFAULT_CONTENT'),
      ruleJson: '',
      redirection: this.block?.options?.redirection === 'url' ? 'url' : 'lp',
      url: this.block?.options?.url,
      landingPage: this.block?.options?.landingPage
    }
  }

  /**
   * Open the rule builder dialog to add rules to the button
   */
  addRulesToButton(): void {
    this.canvasService.openRuleBuilder({ currentElement: this.block });
  }
}
