import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { AbstractControl, FormControl, FormsModule, ReactiveFormsModule, ValidationErrors } from '@angular/forms';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { BehaviorSubject, combineLatest, distinctUntilChanged, map, SubscriptionLike, tap } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { UnicaInput, UnicaInputType } from '@hcl/unica-common';
import { debounce } from '@hcl/angular/unica-angular-common';

@Component({
  selector: 'unica-input',
  standalone: true,
  imports: [
    CommonModule,
    MatFormField,
    MatFormFieldModule,
    FormsModule,
    UnicaIconModule,
    MatInputModule,
    MatButtonModule,
    MatIcon,
    ReactiveFormsModule,
    LetDirective
  ],
  templateUrl: './unica-input.component.html',
  styleUrl: './unica-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UnicaInputComponent implements OnInit, UnicaInput {
  // the type of input
  @Input() type: UnicaInputType = 'text';
  // the label
  @Input() label: string | undefined = undefined;
  // the placeholder
  @Input() placeholder = '';
  // the type of input
  @Input() name = '';
  // is this input clearable
  @Input() clearable = false;
  // hint label
  @Input() hint: string| undefined = undefined;
  // if it should take full width
  @Input() variant: 'full' | 'default' = 'default';
  // prefix Icon
  @Input() prefixIcon: string | undefined;
  // postfix Icon
  @Input() postfixIcon: string | undefined;
  @Input() customClass: string | undefined;
  // disabled the input
  protected _disable = false;
  private isDisabledSubject = new BehaviorSubject(false);
  protected isDisabled$ = this.isDisabledSubject.asObservable().pipe(distinctUntilChanged());
  @Input() set disabled(flg: boolean) {
    if (this.fc) {
      if (flg) {
        this.fc.disable();
      } else {
        this.fc.enable();
      }
    }
  }
  // error translator
  @Input() errorTranslator: ((err: ValidationErrors) => string) | undefined;
  // the value
  private currentValueSubject = new BehaviorSubject<string | number | undefined>(undefined);
  protected currentValue$ = this.currentValueSubject.asObservable().pipe(distinctUntilChanged());
  @Input() set value(v: string | number) {
    this.currentValueSubject.next(v);
  }
  // the form control
  protected fc: FormControl = new FormControl('');
  protected formControlSubject: BehaviorSubject<FormControl> =
    new BehaviorSubject<FormControl>(this.fc);
  @Input() set form(fc: AbstractControl | null) {
    if (fc) {
      this.fc = fc as FormControl;
      this.formControlSubject.next(this.fc);
    }
  }
  // value change subscription
  private valueChangeSubscription: SubscriptionLike | undefined;
  // status change subscription
  private statusChangeSubscription: SubscriptionLike | undefined;
  // flag that tells if this is a password field
  protected isPasswordField = false;
  // the final form control that we will use in the input
  protected form$ = combineLatest([
    this.currentValue$,
    this.formControlSubject.asObservable()
  ]).pipe(
    map(([val, control]) => {
      if (control && val != undefined) {
        control.setValue(val);
      }
      if (this._disable) {
        control.disable();
      } else {
        control.enable();
      }
      this.fc = control;
      return control;
    }),
    tap((fc) => {
      if (fc) {
        if (this.valueChangeSubscription) {
          this.valueChangeSubscription.unsubscribe();
        }
        if (this.statusChangeSubscription) {
          this.statusChangeSubscription.unsubscribe();
        }
        this.valueChangeSubscription = this.fc.valueChanges.subscribe((v) => {
          this.onInputChange(v)
        });
        this.statusChangeSubscription = this.fc.statusChanges.subscribe((v) => {
          if (v === 'DISABLED'){
            this.isDisabledSubject.next(true);
          } else if (v === 'VALID') {
            this.isDisabledSubject.next(false);
          }
        })
      }
    })
  );
  // the dom node instance
  @ViewChild('input') input: ElementRef | undefined;
  // on click of the post fix Icon
  @Output() postfixIconClick: EventEmitter<void> = new EventEmitter<void>();
  // on click of the prefix fix Icon
  @Output() prefixIconClick: EventEmitter<void> = new EventEmitter<void>();
  // on click of the prefix fix Icon
  @Output() valueEnteredEvent: EventEmitter<void> = new EventEmitter<void>();
  /**
   * This function will set the type from password to text & vice-versa
   */
  protected  toggleType() {
    if (this.isPasswordField) {
      this.type = this.type === 'password' ? 'text' : 'password';
    }
  }
  /**
   * set the password field flag
   */
  ngOnInit(): void {
    this.isPasswordField = this.type === 'password';
  }
  /**
   * Get the localized error message from the Forms & return it
   * @param errors
   */
  getError(errors: ValidationErrors | null) {
    if (errors && this.errorTranslator) {
      return this.errorTranslator(errors);
    }
    return '';
  }
  /**
   * Added a debounce so that it is called after 300ms
   */
  @debounce(100)
  public focus() {
    this.input?.nativeElement?.focus();
  }
  /**
   * REturn teh form control
   */
  public getFormControl(): FormControl {
    return this.fc;
  }
  /**
   * Called when the input is changed, we will use this for specific condition
   * like if we have min & max then we restrict the users from going below or above the
   * min & max
   */
  onInputChange(inputValue: string) {
    // chk if form has errors
    if(this.fc.errors  && Object.keys(this.fc.errors).length > 0) {
      // we have errors chk for min & max
      const keys = Object.keys(this.fc.errors);
      const inputElement: HTMLInputElement = this.input?.nativeElement;
      if (inputElement) {
        if (keys.findIndex((v) => v === 'max') >= 0) {
          // we have a max length issue, so we have to update the value
          if (parseInt(inputValue, 10) > this.fc.errors['max']['max']) {
            // we cannot go beyond the max & since value is above max we set it to max
            this.fc.setValue(this.fc.errors['max']['max']);
          }
        }
        if (keys.findIndex((v) => v === 'min') >= 0) {
          // we have a max length issue, so we have to update the value
          if (parseInt(inputValue, 10) < this.fc.errors['min']['min']) {
            // we cannot go beyond the max & since value is above max we set it to max
            this.fc.setValue(this.fc.errors['min']['min']);
          }
        }
      }
    }
  }

  inputChangeEvent(event: Event) {
    this.valueEnteredEvent.emit();
  }
}
