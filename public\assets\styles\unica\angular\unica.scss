@import "material-design-icons/iconfont/material-icons.css";
@import "../../../fonts/HCLTechRoobert/fonts.css";
@import "@angular/cdk/overlay-prebuilt.css";
@import "node_modules/gridstack/dist/gridstack.css";

//Themes that we have in unica
@import "unica-theme";

body, html {
  width: 100%;
  height: 100%;
  margin: 0px;
  font-family: HCLTechRoobert;
}

* {
  box-sizing: border-box;
}
.full-width {
  width: 100%;
}

.full-height{
  height: 100%!important;
}
//DONOT change this else the DND in email editor of elements will have an impact
.cdk-drag-preview {
  display: inline-table;
}
.w-90 {
  width: 90%;
}
.w-80 {
  width: 80%;
}
.w-70 {
  width: 70%;
}
.w-30 {
  width: 30%;
}
.w-20 {
  width: 20%;
}
.w-10 {
  width: 10%;
}
