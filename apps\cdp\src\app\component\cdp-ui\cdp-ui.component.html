<unica-theme [theme]="'light-theme'">
  <ng-container *ngrxLet="userService.userDetailsUpdate$; let userDetails">
    <div class="cdp-container" >
      <div class="cdp-app-bar-container"
           *ngIf="userDetails">
        <cdp-app-bar></cdp-app-bar>
      </div>
      <div
        class="cdp-body-container"
        [ngClass]="{'full-height': !userDetails}">
        <cdp-side-nav></cdp-side-nav>
      </div>
    </div>
  </ng-container>

  <div class="application-spinner" *ngIf="spinnerService.spinner$ | async">
    <unica-spinner
      [align]="'center'"
      [diameter]="50" ></unica-spinner>
  </div>
</unica-theme>

