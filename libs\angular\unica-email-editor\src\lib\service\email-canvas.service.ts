import { Injectable } from '@angular/core';
import { FocusableEmailElement } from '../config/email-element-drop-list';
import {
  BehaviorSubject,
  distinctUntilChanged,
  firstValueFrom,
  map,
  tap,
} from 'rxjs';
import {
  ElementAttribute,
  ElementStyle,
  IDynamicContentData,
  StyleAttribute,
} from '../config/email-common-elements';
import { EmailHistoryImplService } from './email-history-impl.service';
import isEqual from 'lodash-es/isEqual';
import cloneDeep from 'lodash-es/cloneDeep';
import {
  EmailBlock,
  EmailGeneralOptions,
  UnicaEmail,
  UnicaPreferencesUnsubscribeBlock,
  UnicaStructure,
} from '../config/email';
import { EmailDefaultService } from './email-default.service';
import { GenerateUniqueNumber } from '@hcl/unica-common';
import { EmailDropListRegistryService } from './email-drop-list-registry.service';
import { TranslateService } from '@ngx-translate/core';

/**
 * This is a service that has the details about the canvas
 */
@Injectable()
export class EmailCanvasService {
  /**
   * The email that we have right now
   */
  private _email: UnicaEmail | undefined;
  private emailSubject = new BehaviorSubject<UnicaEmail | undefined>(undefined);
  public email$ = this.emailSubject.asObservable().pipe(
    tap((e) => {
      this._email = e;
      this.structureSubject.next(this._email?.structures ?? []);
    }),
  );
  /**
   * Once the email is loaded we need to get the structure & tell all the
   * elements that need to updated based on the structure
   */
  private structureSubject = new BehaviorSubject<UnicaStructure[]>([]);
  public structure$ = this.structureSubject.asObservable().pipe(
    tap((s) => {
      if (this._email) {
        this._email.structures = s;
      }
    }),
  );
  /**
   * The default email options
   * @private
   */
  private _emailOptions: EmailGeneralOptions | undefined = undefined;
  public emailOptions$ = this.email$.pipe(
    map((email) => email?.general ?? undefined),
    tap((o) => {
      if (this._email) {
        this._email.general = o;
      }
    }),
  );

  public dynamicContentInfo = new BehaviorSubject<IDynamicContentData | undefined>(null as any);
  public dynamicContentInfo$ = this.dynamicContentInfo.asObservable();

  public openContentManager = new BehaviorSubject<null>(null as any);
  public openContentManager$ = this.openContentManager.asObservable();
  /**
   * Refresh the data in the Drop list
   */
  private refreshDropListSubject = new BehaviorSubject<string | undefined>(
    undefined,
  );
  public refreshDropList$ = this.refreshDropListSubject.asObservable();

  /**
   * The observable that will fire when the focus is changed
   * @private
   */
  private _focusedElement: FocusableEmailElement | undefined;
  private focusSubject = new BehaviorSubject<FocusableEmailElement | undefined>(
    undefined,
  );
  public focus$ = this.focusSubject.asObservable().pipe(
    distinctUntilChanged(),
    tap((e) => {
      this._focusedElement = e;
    }),
  );
  /**
   * Get the current style of the focused element
   */
  public styleOfFocusedElement$ = this.focus$.pipe(
    map((e) => {
      if (e && e.getStyle()) {
        return e.getStyle();
      }
      return undefined;
    }),
    distinctUntilChanged(),
  );
  /**
   * Get the current style of the focused element
   */
  public attributeOfFocusedElement$ = this.focus$.pipe(
    map((e) => {
      if (e && e.getElementAttribute()) {
        return e.getElementAttribute();
      }
      return undefined;
    }),
    distinctUntilChanged(),
  );

  /**
   * Get the current object of the focused element
   */
  public currentBlockElement$ = this.focus$.pipe(
    map((e) => {
      if (e && e.getCurrentBlock) {
        return e.getCurrentBlock();
      }
      return undefined;
    }),
    distinctUntilChanged(),
  );

  public getCurrentElement(): EmailBlock | undefined {
    return this._focusedElement?.getCurrentBlock ? this._focusedElement.getCurrentBlock() : undefined;
  }
  /**
   * The default constructor
   */
  constructor(
    private historyService: EmailHistoryImplService,
    private defaultService: EmailDefaultService,
    private translate: TranslateService,
    private dropListRegistryService: EmailDropListRegistryService,
  ) {
    // set the default email canvas, so we clear it
    this.clearCanvas();
  }
  /**
   * This function will reset the state of the canvas to the default state
   */
  public clearCanvas(): void {
    // load the default email that we have
    this.defaultService.getDefaultEmail().subscribe((email) => {
      this.emailSubject.next(email);
    });
  }
  /**
   * Set the email Object
   * @param e
   */
  public setEmail(e: UnicaEmail) {
    this.emailSubject.next(e);
  }
  /**
   * This function will be called when user has set thie block
   * as focused
   */
  public setFocus(block: FocusableEmailElement): void {
    setTimeout(() => this.focusSubject.next(block), 100);
  }

  /**
   * This function will update the style of the current focused element
   */
  public updatedFocusedElementStyle<K extends ElementStyle>(
    style: StyleAttribute,
    value: K,
    addToHistory = true,
  ) {
    const element: FocusableEmailElement | undefined = this._focusedElement;
    if (element) {
      // now we have to add to the History stack
      const currentValue = this.getCurrentStyleAttributeValue(style, element);
      if (!isEqual(currentValue, value)) {
        element.updateStyle(style, value);
        if (addToHistory) {
          this.historyService.push({
            execute: this.updatedFocusedElementStyle.bind(
              this,
              style,
              { ...value },
              false,
            ),
            undo: this.updatedFocusedElementStyle.bind(
              this,
              style,
              { ...currentValue },
              false,
            ),
          });
        }
      }
    }
  }

  /**
   * This function will update the style of the current focused element
   */
  public updatedFocusedElementAttribute<K extends ElementAttribute>(
    attr: string,
    value: K,
    addToHistory = true,
  ) {
    const element: FocusableEmailElement | undefined = this._focusedElement;
    if (element) {
      // now we have to add to the History stack
      const currentValue = this.getCurrentElementAttributeValue(attr, element);
      if (!isEqual(currentValue, value)) {
        element.updateElementAttribute(attr, value);
        if (addToHistory) {
          this.historyService.push({
            execute: this.updatedFocusedElementAttribute.bind(
              this,
              attr,
              { ...value },
              false,
            ),
            undo: this.updatedFocusedElementAttribute.bind(
              this,
              attr,
              { ...currentValue },
              false,
            ),
          });
        }
      }
    }
  }
  /**
   * This function will add a block to the canvas
   */
  public async addBlockToCanvas(
    index: number,
    block: EmailBlock,
    isDefaultDesigned = false,
    addToHistory = true,
  ) {
    // need to create a Structure with default config
    let config = await firstValueFrom(
      this.defaultService.getDefaultStructure(isDefaultDesigned),
    );
    // u have the structure, set a new ID for the Structure
    config = { ...config, id: GenerateUniqueNumber(13), elements: [[block]] };
    // now add this block to the canvas
    this.addStructureToCanvas(index, config, false);
    // we have not added to the undo stack now if addToHistory=true, we have to add it now
    if (addToHistory) {
      // since we added a Block, we have added Structure as well, so we remove the structure as well
      this.historyService.push({
        execute: this.addBlockToCanvas.bind(this, index, { ...block }, false),
        undo: this.removeStructureFromCanvas.bind(this, index, false),
      });
    }
  }
  /**
   * Remove the Structure from canvas
   */
  public removeStructureFromCanvas(
    index: number,
    addToHistory = true,
  ): UnicaStructure | undefined {
    if (this._email?.structures) {
      const structure = this._email?.structures;
      if (index < structure?.length) {
        const deletedStructure = structure.splice(index, 1);
        this.structureSubject.next([...structure]);
        if (addToHistory) {
          this.historyService.push({
            execute: this.removeStructureFromCanvas.bind(this, index, false),
            undo: this.addStructureToCanvas.bind(
              this,
              index,
              deletedStructure[0],
              false,
            ),
          });
        }
        return deletedStructure[0];
      }
    }
    return undefined;
  }

  /**
   * This will move a block from a structure block list on to the canvas
   */
  public moveBlockFromDropListToCanvas(
    dropListId: string,
    fromIndex: number,
    toIndex: number,
    addToHistory = true,
  ) {
    const blockDef = this.removeBlockFromDropList(dropListId, fromIndex, false);
    if (blockDef) {
      // we have the block def, now we have to add it to the Structure
      this.addBlockToCanvas(toIndex, blockDef, false);
      if (addToHistory) {
        this.historyService.push({
          execute: this.moveBlockFromDropListToCanvas.bind(
            this,
            dropListId,
            fromIndex,
            toIndex,
            false,
          ),
          undo: this.moveBlockFromDropListToAnotherDropList.bind(
            this,
            '0:' + toIndex,
            toIndex,
            toIndex + ':0',
            0,
            false,
          ),
        });
      }
    }
  }

  /**
   * Move a block form canvas To a Drop list
   */
  public moveBlockFromDropListToAnotherDropList(
    fromDropListId: string,
    fromDropListIndex: number,
    toDropListID: string,
    index: number,
    addToHistory = true,
  ): void {
    if (this.checkIfDropIsNotAllowedToTargetDropList(toDropListID)) {
      return;
    }
    // first remove the block from the Drop list
    const block = this.removeBlockFromDropList(
      fromDropListId,
      fromDropListIndex,
      false,
    );
    // now add this to another drop list
    if (block) {
      this.addBlockToDropList(toDropListID, index, block, false);
      if (addToHistory) {
        this.historyService.push({
          execute: this.moveBlockFromDropListToAnotherDropList.bind(
            this,
            fromDropListId,
            fromDropListIndex,
            toDropListID,
            index,
            false,
          ),
          undo: this.moveBlockFromDropListToAnotherDropList.bind(
            this,
            toDropListID,
            index,
            fromDropListId,
            fromDropListIndex,
            false,
          ),
        });
      }
    }
  }

  /**
   * This function will check if the drop list is not allowed to be dropped on the target drop list
   * this is mainly for the default designed structure which exists for manage communication preferences
   */
  checkIfDropIsNotAllowedToTargetDropList(
    targetDropListId: string,
  ): boolean | undefined {
    const dropList =
      this.dropListRegistryService.getElementDropListById(targetDropListId);
    if (dropList) {
      const arr: string[] = targetDropListId.split(':');
      if (arr.length > 0) {
        const structure = this._email?.structures?.[Number(arr[0])];
        return structure?.options?.isDefaultDesigned;
      }
    }
    return false;
  }

  /**
   * This function will remove a block from the drop list
   */
  public removeBlockFromDropList(
    dropListId: string,
    index: number,
    addToHistory = true,
  ): EmailBlock | undefined {
    // get the drop list instance
    const dropList =
      this.dropListRegistryService.getElementDropListById(dropListId);
    if (dropList) {
      // ok we have the drop list, now get the block that has to be removed from this drop list
      const arr: string[] = dropListId.split(':');
      if (arr.length > 0) {
        // get the Structure that holds this drop list as this structure needs to be changed
        const structure = this._email?.structures?.[Number(arr[0])];
        // now in this structure we have to get the columns
        const ele = structure?.elements?.[Number(arr[1])];
        // now in this element we have to update the index
        const block = ele?.splice(index, 1)[0];
        if (block) {
          //we have to tell the structure that it has been updated & it needs to be refreshed
          this.refreshDropListSubject.next(dropListId);
          // chk if we have to add to history
          if (addToHistory) {
            this.historyService.push({
              execute: this.removeBlockFromDropList.bind(
                this,
                dropListId,
                index,
                false,
              ),
              undo: this.addBlockToDropList.bind(
                this,
                dropListId,
                Number(arr[1]),
                block,
                false,
              ),
            });
          }
          return block;
        }
      }
    }
    return undefined;
  }

  /**
   * This function will add a block to a drop list, this is not for the canvas Drop list
   */
  public addBlockToDropList(
    dropListId: string,
    index: number,
    block: EmailBlock,
    addToHistory = true,
  ): void {
    // get the drop list instance
    const dropList =
      this.dropListRegistryService.getElementDropListById(dropListId);
    if (dropList) {
      // ok we have the drop list, now get the block that has to be removed from this drop list
      const arr: string[] = dropListId.split(':');
      if (arr.length > 0) {
        // get the Structure that holds this drop list as this structure needs to be changed
        const structure = this._email?.structures?.[Number(arr[0])];
        // now in this structure we have to get the columns
        if (structure && !structure.options.isDefaultDesigned) {
          const inx = Number(arr[1]);
          // now structure.elements.length can be less than the number of columns, in that canse we have to add it
          if (structure.elements.length <= inx) {
            while (structure.elements.length <= inx + 1) {
              structure.elements.push([]);
            }
          }
          const ele = structure?.elements?.[inx];
          if (ele) {
            // add this block
            ele.splice(index, 0, block);
            // refresh teh DL
            this.refreshDropListSubject.next(dropListId);
            // chk if we have to add to history
            if (addToHistory) {
              this.historyService.push({
                execute: this.addBlockToDropList.bind(
                  this,
                  dropListId,
                  index,
                  block,
                  false,
                ),
                undo: this.removeBlockFromDropList.bind(
                  this,
                  dropListId,
                  index,
                  false,
                ),
              });
            }
          }
        }
      }
    }
  }
  /**
   * This will be called when user tries to reshuffle the blocks within the structure
   */
  public shuffleBlockInDropList(previousIndex: number,
                                currentIndex: number,
                                dropListId: string,
                                addToHistory = true) {
    // remove the block from previous index
    const block = this.removeBlockFromDropList(dropListId, previousIndex, false);
    // now add this as a new block at a new index
    if (block) {
      this.addBlockToDropList(dropListId, currentIndex, block, false);
      // add to history
      if (addToHistory) {
        // we need to add to history
        this.historyService.push({
          execute: this.shuffleBlockInDropList.bind(this, previousIndex, currentIndex, dropListId, false),
          undo: this.shuffleBlockInDropList.bind(this, currentIndex, previousIndex, dropListId, false),
        });
      }
    }
  }
  /**
   * This function will add the Structure to the canvas
   */
  public addStructureToCanvas(
    index: number,
    config: UnicaStructure,
    addToHistory = true,
  ) {
    // need to update the index if we have added structure in last and default designed structure also exists
    // this is mainly for the default designed structure - for manage communication preferences
    if (this._email?.structures) {
      index = this.updateCurrentIndexIfAddedInLast(
        index,
        this._email?.structures,
        false,
      );
    }
    if (index === this._email?.structures?.length) {
      // we add it at the end directly
      this._email.structures.push(config);
      this.structureSubject.next([...this._email.structures]);
      if (addToHistory) {
        // we need to add to history
        this.historyService.push({
          execute: this.addStructureToCanvas.bind(this, index, config, false),
          undo: this.removeStructureFromCanvas.bind(this, index, false),
        });
      }
    } else {
      const structure = this._email?.structures;
      if (structure) {
        structure.splice(index, 0, config);
        this.structureSubject.next([...structure]);
        if (addToHistory) {
          // we need to add to history
          this.historyService.push({
            execute: this.addStructureToCanvas.bind(this, index, config, false),
            undo: this.removeStructureFromCanvas.bind(this, index, false),
          });
        }
      }
    }
  }

  /**
   * Shuffle the structure
   */
  public shuffleStructure(
    previousIndex: number,
    structure: UnicaStructure,
    currentIndex: number,
    addToHistory = true,
  ) {
    // remove from the prev index
    if (
      this._email?.structures &&
      previousIndex < this._email?.structures?.length
    ) {
      // remove from the current index
      //moveItemInArray(this._email.structures, previousIndex, currentIndex);
      currentIndex = this.updateCurrentIndexIfAddedInLast(
        currentIndex,
        this._email?.structures,
        true,
      );
      const str = this.removeStructureFromCanvas(previousIndex, false);
      if (str) {
        // now add this structure, to the new index
        this.addStructureToCanvas(currentIndex, str, false);
        // if we have add to history, we add it now
        if (addToHistory) {
          // we need to add to history
          this.historyService.push({
            execute: this.shuffleStructure.bind(
              this,
              previousIndex,
              structure,
              currentIndex,
              false,
            ),
            undo: this.shuffleStructure.bind(
              this,
              currentIndex,
              structure,
              previousIndex,
              false,
            ),
          });
        }
      }
    }
  }

  updateCurrentIndexIfAddedInLast(
    currentIndex = 0,
    structures: UnicaStructure[],
    isShuffle: boolean,
  ): number {
    const lengthToCompare = isShuffle
      ? structures.length - 1
      : structures.length;
    if (structures.length > 0) {
      // if the current index is the last index of total structures and that last structure is default designed then
      // we need to shift the current index by minus 1 so that the strucure is reordered jus above the last structure
      // this is mainly for the default designed structure - for manage communication preferences
      if (
        currentIndex === lengthToCompare &&
        structures[structures.length - 1]?.options.isDefaultDesigned
      ) {
        currentIndex = lengthToCompare - 1;
      }
    }
    return currentIndex;
  }
  /**
   * This function will convert the email into a JSON
   */
  public getEmailAsJson(): UnicaEmail | undefined {
    return this._email;
  }
  /**
   * get the style object
   */
  private getCurrentStyleAttributeValue(
    style: StyleAttribute,
    element: FocusableEmailElement,
  ) {
    const s: ElementStyle = element.getStyle();
    if (s) {
      switch (style) {
        case 'padding':
          return {
            padding: s.padding,
          };
        case 'width':
          return {
            width: s.width,
          };
        case 'height':
          return {
            height: s.height,
          };
        case 'background':
          return {
            background: s.background,
          };
        case 'lineHeight':
          return {
            lineHeight: s.lineHeight,
          };
        case 'color':
          return {
            color: s.color,
          };
        case 'margin':
          return {
            margin: s.margin,
          };
        case 'border':
          return {
            border: s.border,
          };
        case 'font':
          return {
            font: s.font,
          };
        case 'fullWidth':
          return {
            fullWidth: s.fullWidth,
          };
        case 'hideOn':
          return {
            hideOn: s.hideOn,
          };
        case 'align':
          return {
            align: s.align,
          };
      }
    }
    return undefined;
  }

  /**
   * get the style object
   */
  private getCurrentElementAttributeValue(
    attr: string,
    element: FocusableEmailElement,
  ) {
    const s: ElementAttribute | undefined = element.getElementAttribute();
    if (s) {
      switch (attr) {
        case 'innerText':
          return {
            innerText: s.innerText
          }
        case 'rules':
          return {
            rules: s.rules
          }
        case 'src':
          return {
            src: s.src
          }
        case 'downloadDetails': 
          return  {
            downloadDetails: s.downloadDetails
          }
      }
    }
    return undefined;
  }

  /**
   * method to add manage unsubscribe block
   */
  public addManageUnsubscribeBlock() {
    if (this._email?.structures) {
      this.addBlockToCanvas(
        this._email.structures.length,
        new UnicaPreferencesUnsubscribeBlock(
          true,
          false,
          this.translate.instant(
            'EMAIL_EDITOR.LABELS.MANAGE_COMMUNICATIONS_PREFERENCES',
          ),
          this.translate.instant(
            'EMAIL_EDITOR.LABELS.MANAGE_COMMUNICATIONS_PREFERENCES',
          ),
        ),
        true,
        true,
      );
    }
  }

  /**
   * method to remove manage unsubscribe block
   */
  public removeManageUnsubscribeBlock() {
    if (this._email?.structures && this._email.structures.length > 0) {
      if (
        this._email.structures[this._email.structures.length - 1].options
          .isDefaultDesigned
      ) {
        this.removeStructureFromCanvas(this._email.structures.length - 1, true);
      }
    }
  }

  /**
   * method to check if manage unsubscribe block exists to set toggle
   */
  public getIsManagePreferences(): boolean | undefined {
    if (this._email?.structures && this._email.structures.length > 0) {
      return this._email.structures[this._email.structures.length - 1].options
        .isDefaultDesigned;
    }
    return false;
  }

  /**
   * Duplicate a structure at the specified index
   * @param index The index of the structure to duplicate
   * @param addToHistory Whether to add this operation to the history stack
   * @returns The duplicated structure or undefined if the operation failed
   */
  public duplicateStructure(
    index: number,
    addToHistory = true,
  ): UnicaStructure | undefined {
    const structures = this._email?.structures;
    if (!structures || index >= structures.length) {
      return undefined;
    }

    const originalStructure = structures[index];

    // Skip if it's a default designed structure (like manage preferences)
    if (originalStructure.options.isDefaultDesigned) {
      return undefined;
    }

    // Create a deep clone with a new ID
    const duplicatedStructure: UnicaStructure = {
      ...cloneDeep(originalStructure),
      id: GenerateUniqueNumber(13),
    };

    // Insert after the original structure
    const insertIndex = index + 1;
    this.addStructureToCanvas(insertIndex, duplicatedStructure, false);

    // Add to history if needed
    if (addToHistory) {
      this.historyService.push({
        execute: this.duplicateStructure.bind(this, index, false),
        undo: this.removeStructureFromCanvas.bind(this, insertIndex, false),
      });
    }

    return duplicatedStructure;
  }

  /**
   * Remove a block from a drop list
   * @param dropListId The ID of the drop list (format: "structureIndex:columnIndex")
   * @param blockIndex The index of the block to remove
   * @param addToHistory Whether to add this operation to the history stack
   * @returns True if the block was successfully removed, false otherwise
   */
  public removeBlockById(
    dropListId: string,
    blockIndex: number,
    addToHistory = true,
  ): boolean {
    // Remove the block from the drop list
    const removedBlock = this.removeBlockFromDropList(
      dropListId,
      blockIndex,
      addToHistory,
    );
    return !!removedBlock;
  }

  /**
   * Duplicate a block in a drop list
   * @param dropListId The ID of the drop list (format: "structureIndex:columnIndex")
   * @param blockIndex The index of the block to duplicate
   * @param addToHistory Whether to add this operation to the history stack
   * @returns True if the block was successfully duplicated, false otherwise
   */
  public duplicateBlockById(
    dropListId: string,
    blockIndex: number,
    addToHistory = true,
  ): boolean {
    const [structureIndex, columnIndex] = dropListId.split(':').map(Number);
    if (isNaN(structureIndex) || isNaN(columnIndex)) {
      return false;
    }

    const structure = this._email?.structures?.[structureIndex];
    if (!structure || structure.options.isDefaultDesigned) {
      return false;
    }

    const column = structure.elements?.[columnIndex];
    if (!column || blockIndex >= column.length) {
      return false;
    }

    // Clone and insert the block after the original
    const duplicatedBlock = cloneDeep(column[blockIndex]);
    this.addBlockToDropList(dropListId, blockIndex + 1, duplicatedBlock, addToHistory);

    return true;
  }

  /**
   * Find the position of a block by its component reference
   * @param targetBlock The block to find
   * @returns Object with dropListId and blockIndex, or null if not found
   */
  private findBlockPosition(targetBlock: EmailBlock): { dropListId: string; blockIndex: number } | null {
    const structures = this._email?.structures;
    if (!structures) return null;

    for (let structureIndex = 0; structureIndex < structures.length; structureIndex++) {
      const structure = structures[structureIndex];

      // Skip default designed structures
      if (structure.options.isDefaultDesigned) continue;

      for (let columnIndex = 0; columnIndex < structure.elements.length; columnIndex++) {
        const column = structure.elements[columnIndex];

        for (let blockIndex = 0; blockIndex < column.length; blockIndex++) {
          if (column[blockIndex] === targetBlock) {
            return {
              dropListId: `${structureIndex}:${columnIndex}`,
              blockIndex
            };
          }
        }
      }
    }

    return null;
  }

  /**
   * Remove a block by finding its component reference in the email structure
   * This is a fallback method for saved layouts where blocks have original component IDs
   * @param blockComponent The block component to remove
   * @param addToHistory Whether to add this operation to the history stack
   * @returns True if the block was successfully removed, false otherwise
   */
  public removeBlockByComponent(
    blockComponent: { block: EmailBlock },
    addToHistory = true,
  ): boolean {
    if (!blockComponent?.block) return false;

    const position = this.findBlockPosition(blockComponent.block);
    return position ? this.removeBlockById(position.dropListId, position.blockIndex, addToHistory) : false;
  }

  /**
   * Duplicate a block by finding its component reference in the email structure
   * This is a fallback method for saved layouts where blocks have original component IDs
   * @param blockComponent The block component to duplicate
   * @param addToHistory Whether to add this operation to the history stack
   * @returns True if the block was successfully duplicated, false otherwise
   */
  public duplicateBlockByComponent(
    blockComponent: { block: EmailBlock },
    addToHistory = true,
  ): boolean {
    if (!blockComponent?.block) return false;

    const position = this.findBlockPosition(blockComponent.block);
    return position ? this.duplicateBlockById(position.dropListId, position.blockIndex, addToHistory) : false;
  }

  /**
   * This method will be called when the user wants to add / edit a rule
   * @param event This event is used to open the rule builder
   */
  openRuleBuilder(event: IDynamicContentData) {
    if (event) {
        this.dynamicContentInfo.next(event);
    }
  }

   /**
   * This method will be called when user wants to open the content picker
   */
   openContentPicker() {
      this.openContentManager.next(null);
  }
}
