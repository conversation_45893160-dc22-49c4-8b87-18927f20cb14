import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ButtonDynamicSettingFormComponent } from './button-dynamic-setting-form.component';

describe('ButtonDynamicSettingFormComponent', () => {
  let component: ButtonDynamicSettingFormComponent;
  let fixture: ComponentFixture<ButtonDynamicSettingFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ButtonDynamicSettingFormComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonDynamicSettingFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
