import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdpUserService } from '../../service/cdp-user.service';
import { ReplaySubject, tap, withLatestFrom } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { CdpDashboardService } from '../../service/cdp-dashboard.service';
import { ActivatedRoute } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { UnicaDashboardBuilderModule } from '@hcl/angular/unica-dashboard-builder';

/**
 * The default dashboard for CDP
 */
@UntilDestroy()
@Component({
  selector: 'cdp-dashboard',
  standalone: true,
  imports: [CommonModule, LetDirective, UnicaDashboardBuilderModule],
  templateUrl: './cdp-dashboard.component.html',
  styleUrl: './cdp-dashboard.component.scss',
  providers: [CdpDashboardService]
})
export class CdpDashboardComponent implements OnInit{
  /**
   * The type of dashboard
   * @private
   */
  private dashboardTypeSubject: ReplaySubject<string> = new ReplaySubject(1);
  /**
   * The default of the dashboard
   */
  private dashboardType$ = this.dashboardTypeSubject.asObservable().pipe(
    withLatestFrom(this.userService.tenantDetailsUpdate$),
    tap(([type, tenantDetails]) => {
      // tell the dashboard service to use this value
      const navConfig = tenantDetails?.config.navConfig;
      if (navConfig) {
        const def = navConfig[type] as {id: number};
        this.dashboardService.init(def.id);
      }
    })
  );
  /**
   * The dashboard def
   */
  protected dashboardDef$ = this.dashboardService.dashboardDef$;
  /**
   *  the def constructor
   */
  constructor(private userService: CdpUserService,
              private route: ActivatedRoute,
              private dashboardService: CdpDashboardService) {
    this.dashboardType$.pipe(untilDestroyed(this)).subscribe();
  }
  /**
   * Based on routing get the def
   */
  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const type = params['type'];
      this.dashboardTypeSubject.next(type);
    });
  }

}
