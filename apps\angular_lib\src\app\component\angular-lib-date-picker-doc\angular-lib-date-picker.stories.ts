import { applicationConfig, componentWrapperDecorator, Meta, StoryObj } from '@storybook/angular';
import { UnicaDatePickerComponent } from '@hcl/angular/unica-date-picker';
import { provideAnimations } from '@angular/platform-browser/animations';

export const ActionsData = {};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaDatePickerComponent> = {
  title: 'Unica Date Picker',
  component: UnicaDatePickerComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will render a date component in the unica application'
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [provideAnimations()],
    }),
    componentWrapperDecorator((story) => `<div class="light-theme">${story}</div>`)],

  tags: ['autodocs'],
  argTypes: {
    label: {
      type: 'string',
      description:
        'The label that we need to display for the text-box this needs to be a translated label',
    },
    placeHolder: {
      type: 'string',
      description:
        'The Place holder for the start date this needs to be a translated',
    },
    minDate: {
        type: 'string',
        description:
          'Define a minimum date, So the user not allowed to select the date before this date',
      },
      maxDate: {
        type: 'string',
        description:
          'Define a maximum date, So the user not allowed to select the date after this date',
      },
      dateFormat: {
        type: 'string',
        description:
          'Define a date format, So the user can see the date in the format that is defined',
      },
    hint: {
      type: 'string',
      description:
        'This is a string that will be displayed below the date picker that will help user' +
        'to identify what this text box represents.',
    },
    disabled: {
      description:
        'Based on this value the date picker will be enabled or disabled',
    },
    errorTranslator: {
      type: 'function',
      description:
        'We will use Reactive forms & these forms will have some validations associated' +
        'to them, in these cases in case there are some errors that need to be displayed' +
        'this component will call this function & send the error as param to the function, the' +
        'function needs to return the translated error message that needs to be displayed' +
        'as error on the date picker box."(err: ValidationError) => string"',
    },
    form: {
      type: 'function',
      description: 'The form group for the date picker box'
    }
  },

};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaDatePickerComponent>;

/**
 * The default date picker box 
 */
export const Default: Story = {
  args: {
    label: 'Enter A Date',
    disabled: false,
    dateFormat: 'DD/MM/YYYY'
  },
}
