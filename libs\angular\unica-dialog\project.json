{"name": "unica-dialog", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/angular/unica-dialog/src", "prefix": "hcl", "projectType": "library", "tags": ["lib:angular-dialog", "lib:angular"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/angular/unica-dialog/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/angular/unica-dialog/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/angular/unica-dialog/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/angular/unica-dialog/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}