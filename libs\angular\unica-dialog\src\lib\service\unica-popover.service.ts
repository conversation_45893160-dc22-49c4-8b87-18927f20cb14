import { Injectable } from '@angular/core';
import {
  Overlay,
  OverlayRef,
  OverlayPositionBuilder,
  ConnectedPosition,
  FlexibleConnectedPositionStrategy,
  OverlayConfig,
} from '@angular/cdk/overlay';
import { ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

/**
 * Configuration options for the popover
 */
export interface PopoverConfig {
  /** Whether the popover has a backdrop */
  hasBackdrop?: boolean;
  /** The backdrop class */
  backdropClass?: string;
  /** The width of the popover */
  width?: string | number;
  /** The height of the popover */
  height?: string | number;
  /** The min-width of the popover */
  minWidth?: string | number;
  /** The min-height of the popover */
  minHeight?: string | number;
  /** The max-width of the popover */
  maxWidth?: string | number;
  /** The max-height of the popover */
  maxHeight?: string | number;
  /** Whether the popover can be closed by clicking outside */
  closeOnBackdropClick?: boolean;
  /** The class to add to the popover panel */
  panelClass?: string | string[];
  /** Preferred positions in order of priority */
  positions?: ConnectedPosition[];
}

/**
 * Default positions for the popover to try in order of priority
 */
const DEFAULT_POSITIONS: ConnectedPosition[] = [
  {
    // Below center
    originX: 'center',
    originY: 'bottom',
    overlayX: 'center',
    overlayY: 'top',
    offsetY: 8,
  },
  {
    // Above center
    originX: 'center',
    originY: 'top',
    overlayX: 'center',
    overlayY: 'bottom',
    offsetY: -8,
  },
  {
    // Right center
    originX: 'end',
    originY: 'center',
    overlayX: 'start',
    overlayY: 'center',
    offsetX: 8,
  },
  {
    // Left center
    originX: 'start',
    originY: 'center',
    overlayX: 'end',
    overlayY: 'center',
    offsetX: -8,
  },
];

/**
 * Default popover configuration
 */
const DEFAULT_CONFIG: PopoverConfig = {
  hasBackdrop: true,
  backdropClass: 'cdk-overlay-transparent-backdrop',
  closeOnBackdropClick: true,
  positions: DEFAULT_POSITIONS,
};

/**
 * A service for displaying dynamic popover components anchored to elements in the DOM.
 */
@Injectable({ providedIn: 'root' })
export class UnicaPopoverService {
  private overlayRef: OverlayRef | null = null;
  private destroySubject = new Subject<void>();

  constructor(
    private overlay: Overlay,
    private positionBuilder: OverlayPositionBuilder,
  ) {}

  /**
   * Opens a popover with the provided component.
   *
   * @param component - The component to render in the popover
   * @param attachTo - The element to attach the popover to
   * @param data - Optional data to pass to the component
   * @param config - Optional configuration for the popover
   * @returns The instance of the created component
   *
   * @example
   * ```ts
   * // Open a simple popover with default settings
   * const instance = popoverService.openPopover(MyComponent, buttonElement);
   *
   * // Open a popover with data and custom configuration
   * const instance = popoverService.openPopover(
   *   MyFormComponent,
   *   buttonElement,
   *   { initialValue: 'test' },
   *   { width: '300px', closeOnBackdropClick: false }
   * );
   *
   * // Listen to events from the component
   * instance.saved.subscribe(result => console.log(result));
   * ```
   */
  openPopover<T extends object>(
    component: ComponentType<T>,
    attachTo: HTMLElement,
    data?: Partial<T>,
    config?: PopoverConfig,
  ): T {
    this.closePopover();

    // Reset the destroy subject
    this.destroySubject = new Subject<void>();

    // Merge default config with provided config
    const finalConfig: PopoverConfig = { ...DEFAULT_CONFIG, ...config };

    // Create the position strategy
    const positionStrategy = this.createPositionStrategy(
      attachTo,
      finalConfig.positions,
    );

    // Create the overlay config
    const overlayConfig = this.createOverlayConfig(
      finalConfig,
      positionStrategy,
    );

    // Create the overlay
    this.overlayRef = this.overlay.create(overlayConfig);

    // Create the component portal
    const portal = new ComponentPortal(component);

    // Attach the portal to the overlay
    const componentRef = this.overlayRef.attach(portal);

    // Get the component instance
    const instance = componentRef.instance;

    // Pass data to the component instance
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        (instance as Record<string, unknown>)[key] = value;
      });
    }

    // Handle backdrop clicks
    if (finalConfig.hasBackdrop && finalConfig.closeOnBackdropClick) {
      this.overlayRef
        .backdropClick()
        .pipe(takeUntil(this.destroySubject))
        .subscribe(() => this.closePopover());
    }

    return instance;
  }

  /**
   * Closes the currently open popover if any
   */
  closePopover(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }

    this.destroySubject.next();
  }

  /**
   * Creates a position strategy for the popover
   */
  private createPositionStrategy(
    element: HTMLElement,
    positions?: ConnectedPosition[],
  ): FlexibleConnectedPositionStrategy {
    const strategy = this.positionBuilder
      .flexibleConnectedTo(element)
      .withPush(true)
      .withViewportMargin(10)
      .withGrowAfterOpen(true)
      .withPositions(positions || DEFAULT_POSITIONS);

    return strategy;
  }

  /**
   * Creates the overlay config for the popover
   */
  private createOverlayConfig(
    config: PopoverConfig,
    positionStrategy: FlexibleConnectedPositionStrategy,
  ): OverlayConfig {
    const overlayConfig = new OverlayConfig({
      positionStrategy,
      hasBackdrop: config.hasBackdrop,
      backdropClass: config.backdropClass,
      width: config.width,
      height: config.height,
      minWidth: config.minWidth,
      minHeight: config.minHeight,
      maxWidth: config.maxWidth,
      maxHeight: config.maxHeight,
      panelClass: config.panelClass,
      scrollStrategy: this.overlay.scrollStrategies.reposition(),
    });

    return overlayConfig;
  }
}
