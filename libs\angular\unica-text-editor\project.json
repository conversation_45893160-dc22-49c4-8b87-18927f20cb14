{"name": "unica-text-editor", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/angular/unica-text-editor/src", "prefix": "unica", "projectType": "library", "tags": ["lib:angular-text-editor", "lib:angular"], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/angular/unica-text-editor/ng-package.json"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/angular/unica-text-editor/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}