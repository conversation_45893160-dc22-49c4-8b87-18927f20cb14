import { TemplateRef } from '@angular/core';

export interface ModalConfig {
  disableClose?: boolean; // Whether the user can use escape or clicking on the backdrop to close the modal.
  backdropClass?: string;
  hasBackdrop?: boolean;
  height?: string;
  width?: string;
  actionsTemplate?: TemplateRef<unknown>;
  contentTemplate?: TemplateRef<unknown>;
  titleTemplate?: TemplateRef<unknown>;
  content?: string;
  title?: string;
  styleClass?: string; // Class applied to the dialog panel
  containerClass?: string; // Class applied to the mat-mdc-dialog-container
  data?: object;
  resizeable?: boolean;
  // Dialog action properties
  confirmActionTemplate?: boolean;
  alertActionTemplate?: boolean;
  okLabel?: string;
  cancelButton?: string;
  okCallback?: () => void;
  cancelCallback?: () => void;
}
export interface ButtonConf {
  value?: string;
  disabled?: boolean;
  // color of button
  color?: 'primary' | 'warn' | 'accent';
  // button type according to mat ui
  buttonType: 'icon' | 'fab' | 'mat' | 'stroked' | 'raised' | 'flat';
  icon?: string;
  iconRight?: string;
  isIconButton?: boolean;
  borderRadius?: number;
  styleClass?: string;
  title?: string;
  id?: string;
  name: string;
  type?: 'reset' | 'submit' | 'button';
}
