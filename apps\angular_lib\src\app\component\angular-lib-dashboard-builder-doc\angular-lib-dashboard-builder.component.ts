import {
  AfterContentInit,
  ChangeDetectionStrategy,
  Component,
  ContentChildren,
  QueryList, TemplateRef,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaDashboardBuilderComponent, UnicaDashboardBuilderModule } from '@hcl/angular/unica-dashboard-builder';
import { UnicaDashboardOptions } from '@hcl/unica-common';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { UnicaTemplateDirective } from '@hcl/angular/unica-angular-common';

@Component({
  selector: 'angular-lib-dashboard-builder',
  standalone: true,
  imports: [CommonModule, UnicaDashboardBuilderModule, UnicaButtonModule, UnicaTemplateDirective],
  templateUrl: './angular-lib-dashboard-builder.component.html',
  styleUrl: './angular-lib-dashboard-builder.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AngularLibDashboardBuilderComponent {
  /**
   * The instance of db builder
   */
  @ViewChild('dashboard') dashboard: UnicaDashboardBuilderComponent | undefined;
  /**
   * The default configuration for the Dashboard
   * @protected
   */
  protected dashboardOptions: UnicaDashboardOptions = {
    column: 48
  }
  /**
   * Add a container to the DB
   * @protected
   */
  protected addContainer(): void {
    if (this.dashboard) {
      this.dashboard.addElement({
        x: 0,
        y: 0,
        w: 12,
        h: 12,
        id: Math.random() + '',
        templateName: 'template' + (Math.floor(Math.random())%3)
      })
    }
  }
}
