import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { UnicaNumberSpinnerComponent } from '@hcl/angular/unica-input';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { ElementLineHeight } from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, tap } from 'rxjs';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { UnicaSlideToggleComponent } from '@hcl/angular/unica-slide-toggle';
import { ElementLineHeightFormService } from '../../../service/form/element-line-height-form.service';
import { UnicaDropdownComponent } from '@hcl/angular/unica-dropdown';

@UntilDestroy()
@Component({
  selector: 'element-line-height-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaNumberSpinnerComponent, UnicaTypographyComponent, UnicaDropdownComponent, UnicaSlideToggleComponent],
  providers: [ElementLineHeightFormService],
  templateUrl: './element-line-height-form.component.html',
  styleUrl: './element-line-height-form.component.scss',
})
export class ElementLineHeightFormComponent {
  /**
   * Get the style of the current element
   */
  private elementStyle$ = this.canvasService.styleOfFocusedElement$
  /**
   * The line height
   */
  private _lineHeight: ElementLineHeight | undefined;
  private lineHeightSubject = new BehaviorSubject<ElementLineHeight | undefined>(undefined);
  protected height$ = this.lineHeightSubject.asObservable().pipe(tap(x => this._lineHeight = x));
  @Input() set lineHeight(v: ElementLineHeight | undefined) {
    this.lineHeightSubject.next(v);
    if (v) {
      this.form.setValue(v);
    }
  }
  lineHeightUnitOptions: string[] = this.lineHeightForm.lineHeightUnitOptionsList;
  /**
   * the line height Form Group
   */
  protected readonly form: FormGroup;
  /**
   * Default constructor
   */
  constructor(private lineHeightForm: ElementLineHeightFormService,
              private translate: TranslateService,
              private canvasService: EmailCanvasService) {
    this.form = this.lineHeightForm.form;
    this.elementStyle$.pipe(untilDestroyed(this)).subscribe((s) => {
      this.lineHeight = s?.lineHeight;
    });
    // tell the canvas service to change
    this.lineHeightForm.valueChange$.pipe(distinctUntilChanged((previous, current) => {
      if (previous && current &&
        previous.value === current.value &&
        previous.unit === current.unit )
        return true;
      return false;
    }),tap((v) => {
        
    }),
    untilDestroyed(this)).subscribe((v) => {
      this.canvasService.updatedFocusedElementStyle('lineHeight', {lineHeight: v});
    });
  }
  /**
   * Handle the errors on the fields
   * @protected
   */
  protected errorTranslator(field: string,error: ValidationErrors): string {
    if (error) {
      const errorList = Object.keys(error);
      if(errorList.length > 0) {
        // the 0th key represents the error
        return this.translate.instant('UNICA_COMMON.ERRORS.' + errorList[0].toUpperCase(),
          { field: this.translate.instant(field), ...error });
      }
    }
    return '';
  }
}
