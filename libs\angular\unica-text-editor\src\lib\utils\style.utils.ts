/**
 * Style utility functions for Unica Text Editor
 * Provides reusable functions for style computation and manipulation
 */

import { TextEditorStyles } from '../editor.model';

/**
 * Converts TextEditorStyles to CSS style object
 * @param styles - The TextEditorStyles object
 * @returns Record<string, string> - CSS styles object
 */
export function computeEditorStyles(
  styles?: TextEditorStyles,
): Record<string, string> {
  if (!styles) return {};

  const { color, font, lineHeight, padding } = styles;

  return {
    ...getColorStyles(color),
    ...getFontStyles(font),
    ...getLineHeightStyles(lineHeight),
    ...getPaddingStyles(padding),
  };
}

/**
 * Get color styles
 * @param color - Color value
 * @returns CSS color styles
 */
export function getColorStyles(color?: string): Record<string, string> {
  return color ? { color } : {};
}

/**
 * Get font styles
 * @param font - Font configuration object
 * @returns CSS font styles
 */
export function getFontStyles(
  font?: TextEditorStyles['font'],
): Record<string, string> {
  if (!font) return {};

  const styles: Record<string, string> = {};

  if (font.family) styles['font-family'] = font.family;
  if (font.size) styles['font-size'] = `${font.size}px`;
  if (font.weight) styles['font-weight'] = font.weight;
  if (font.style) styles['font-style'] = font.style;

  return styles;
}

/**
 * Get line height styles
 * @param lineHeight - Line height configuration object
 * @returns CSS line-height styles
 */
export function getLineHeightStyles(
  lineHeight?: TextEditorStyles['lineHeight'],
): Record<string, string> {
  return lineHeight?.value && lineHeight?.unit
    ? { 'line-height': `${lineHeight.value}${lineHeight.unit}` }
    : {};
}

/**
 * Get padding styles
 * @param padding - Padding configuration object
 * @returns CSS padding styles
 */
export function getPaddingStyles(
  padding?: TextEditorStyles['padding'],
): Record<string, string> {
  if (!padding || !hasPaddingValues(padding)) return {};

  const { top = 0, right = 0, bottom = 0, left = 0 } = padding;
  return { padding: `${top}px ${right}px ${bottom}px ${left}px` };
}

/**
 * Check if padding object has any values
 * @param padding - Padding configuration object
 * @returns boolean - True if any padding value is set
 */
export function hasPaddingValues(
  padding: NonNullable<TextEditorStyles['padding']>,
): boolean {
  return !!(padding.top || padding.right || padding.bottom || padding.left);
}
