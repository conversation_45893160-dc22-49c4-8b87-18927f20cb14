import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaAppBarComponent } from '@hcl/angular/unica-app-bar';
import { UnicaSearchComponent } from '@hcl/angular/unica-input';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UnicaMenuComponent, UnicaMenuItemComponent, UnicaUserMenuComponent } from '@hcl/angular/unica-menu';
import { CdpUserService } from '../../service/cdp-user.service';
import { LetDirective } from '@ngrx/component';
import { UnicaButtonModule } from '@hcl/angular/unica-button';

@Component({
  selector: 'cdp-app-bar',
  standalone: true,
  imports: [
    CommonModule,
    UnicaAppBarComponent,
    UnicaSearchComponent,
    UnicaTypographyComponent,
    UnicaMenuComponent,
    LetDirective,
    UnicaButtonModule,
    UnicaUserMenuComponent,
    UnicaMenuItemComponent
  ],
  templateUrl: './cdp-app-bar.component.html',
  styleUrl: './cdp-app-bar.component.scss',
})
export class CdpAppBarComponent {

  protected user$ = this.userService.userDetailsUpdate$;
  /**
   *
   * @param spinnerService
   */
  constructor(protected userService: CdpUserService) {
  }
  /**
   * User clicked on logout, so logout the user
   */
  public logoutUer() {
    console.info('logout user')
  }
}
