import { applicationConfig, componentWrapperDecorator, Meta, StoryObj } from '@storybook/angular';
import { UnicaDateRangePickerComponent } from '@hcl/angular/unica-date-picker';
import { provideAnimations } from '@angular/platform-browser/animations';

export const ActionsData = {};

/**
 * Create the metadata for the Component
 */
const meta: Meta<UnicaDateRangePickerComponent> = {
  title: 'Unica Date Range Picker',
  component: UnicaDateRangePickerComponent,
  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,
  parameters: {
    docs: {
      description: {
        component:
          'This is the component that will render a date range picker component in the unica application'
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [provideAnimations()],
    }),
    componentWrapperDecorator((story) => `<div class="light-theme">${story}</div>`)],

  tags: ['autodocs'],
  argTypes: {
    label: {
      type: 'string',
      description:
        'The label that we need to display for the text-box this needs to be a translated label',
    },
    startDatePlaceHolder: {
      type: 'string',
      description:
        'The Place holder for the start date this needs to be a translated',
    },
    endDatePlaceHolder: {
      type: 'string',
      description:
        'The Place holder for the end date this needs to be a translated',
    },
    enabledDateRangeSelection:{
      type: 'boolean',
      description:
        'This will enable the user to select the date range from the date picker',
    },
    numberOfDays: {
      type: 'number',
      description:
        'The number of days that we need to show in the date date range-picker box',
    },
    minDate: {
        type: 'string',
        description:
          'Define a minimum date, So the user not allowed to select the date before this date',
      },
      maxDate: {
        type: 'string',
        description:
          'Define a maximum date, So the user not allowed to select the date after this date',
      },
      dateFormat: {
        type: 'string',
        description:
          'Define a date format, So the user can see the date in the format that is defined',
      },
    hint: {
      type: 'string',
      description:
        'This is a string that will be displayed below the date picker that will help user' +
        'to identify what this text box represents.',
    },
    disabled: {
      description:
        'Based on this value the date picker will be enabled or disabled',
    },
    errorTranslator: {
      type: 'function',
      description:
        'We will use Reactive forms & these forms will have some validations associated' +
        'to them, in these cases in case there are some errors that need to be displayed' +
        'this component will call this function & send the error as param to the function, the' +
        'function needs to return the translated error message that needs to be displayed' +
        'as error on the date range picker box."(err: ValidationError) => string"',
    },
    form: {
      type: 'function',
      description: 'The form group for the date range-picker box'
    }
  },

};
export default meta;

/**
 * Let's now create stories for the component
 */
type Story = StoryObj<UnicaDateRangePickerComponent>;

/**
 * The default date range-picker box 
 */
export const Default: Story = {
  args: {
    label: 'Enter A Date',
    startDatePlaceHolder: 'Start Date',
    endDatePlaceHolder: 'End Date',
    disabled: false,
    dateFormat: 'DD/MM/YYYY'
  },
}
