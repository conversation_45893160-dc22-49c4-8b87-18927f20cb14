# unica-text-editor

A rich text editor component for the Unica application using Quill.

## Usage

```typescript
import { UnicaTextEditorComponent } from '@hcl/angular/unica-text-editor';

@Component({
  selector: 'app-my-component',
  standalone: true,
  imports: [UnicaTextEditorComponent],
  template: `
    <unica-text-editor
      [content]="myContent"
      (contentChange)="onContentChange($event)"
      [readOnly]="false"
      [placeholder]="'Enter text here...'">
    </unica-text-editor>
  `
})
export class MyComponent {
  myContent = '<p>Initial content</p>';
  
  onContentChange(newContent: string) {
    console.log('Content changed:', newContent);
  }
}
```

## Styling

✅ **No additional style imports required!**

All Quill styles are now automatically bundled with the component. The component uses `ViewEncapsulation.None` to ensure Quill styles work properly, but all styles are scoped to the component.

### Migration from Previous Versions

If you were previously importing Quill styles manually, you can now remove them:

```scss
// ❌ Remove these imports from your global styles
@import '@hcl/angular/unica-text-editor/styles/quill.scss';
@import 'node_modules/quill/dist/quill.core.css';
@import 'node_modules/quill/dist/quill.bubble.css';
```

And remove from your angular.json styles array:

```json
// ❌ Remove from angular.json
"styles": [
  "node_modules/@hcl/angular/unica-text-editor/styles/quill.scss"
]
```

## Properties

- `content`: The content to display in the editor (input)
- `contentChange`: Event emitter for content changes (output)
- `readOnly`: Whether the editor is read-only (input, default: false)
- `placeholder`: Placeholder text for empty editor (input, default: 'Enter text here...')

## Running unit tests

Run `nx test unica-text-editor` to execute the unit tests.
