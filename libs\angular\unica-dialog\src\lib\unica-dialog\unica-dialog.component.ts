import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ElementRef,
  Inject,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { ButtonConf, ModalConfig } from './unica-dialog.model';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'unica-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, UnicaButtonModule, TranslateModule],
  templateUrl: './unica-dialog.component.html',
  styleUrl: './unica-dialog.component.scss',
  // We can keep ViewEncapsulation.None for component-specific styles
  encapsulation: ViewEncapsulation.None,
  // Add host class to connect with global styles
  host: {
    class: 'unica-dialog'
  }
})
export class UnicaDialogComponent implements AfterViewInit {
  /**
   * the confirmation dialog Action buttons
   */
  @ViewChild('confirmActionTemplate', { static: false })
  confirmActionTemplate!: TemplateRef<ElementRef>;
  /**
   * the alert dialog Action buttons
   */
  @ViewChild('alertActionTemplate', { static: false })
  alertActionTemplate!: TemplateRef<ElementRef>;
  /**
   * The conent in whcih u can add HTML
   */
  @ViewChild('contentHtml', { static: false })
  contentHtml!: TemplateRef<ElementRef>;
  /**
   * the button configuration for the cancel button
   */
  cancelButtonConfig: ButtonConf = {
    color: 'accent',
    value: 'Cancel',
    buttonType: 'stroked',
    borderRadius: 5,
    name: 'basicButton',
    type: 'button',
    styleClass: 'medium-btn',
  };
  /**
   * The button configuration for the ok button
   */
  okButtonConfig: ButtonConf = {
    color: 'accent',
    value: 'OK',
    buttonType: 'stroked',
    borderRadius: 5,
    name: 'basicButton',
    type: 'button',
    styleClass: 'medium-btn',
  };
  /**
   * In case we want to use the in build Action template name
   */
  inBuildActionTemplateName!: TemplateRef<unknown> | null;
  /**
   * the default constructor
   * param {MatDialogRef<ModalComponent>} dialogRef
   * param {ModalConfig} data
   */
  constructor(
    public dialogRef: MatDialogRef<UnicaDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ModalConfig,
  ) {
    // we are loading the confirm action template
    if (data['confirmActionTemplate']) {
      // in such case load the localized buttins
      this.okButtonConfig.value = data['okLabel'];
      this.cancelButtonConfig.value = data['cancelButton'];
    } else if (data['alertActionTemplate']) {
      this.okButtonConfig.value = data['okLabel'];
    }
  }
  /**
   * After we initializa lets set the template
   */
  ngAfterViewInit(): void {
    if (this.data['confirmActionTemplate']) {
      this.data.actionsTemplate = this.confirmActionTemplate;
      this.data.contentTemplate = this.contentHtml;
      this.inBuildActionTemplateName = this.confirmActionTemplate;
    }
    if (this.data['alertActionTemplate']) {
      this.data.actionsTemplate = this.alertActionTemplate;
      this.data.contentTemplate = this.contentHtml;
      this.inBuildActionTemplateName = this.alertActionTemplate;
    }
  }
}







